"""
Gematria Explorer Panel - Unified gematria analysis interface.

This panel combines the functionality of DocumentAnalysisPanel and GematriaDictionaryPanel
into a single, cohesive interface for exploring gematria relationships in documents.

Key features:
- Interactive document exploration with real-time gematria calculation
- Deep document analysis with statistical patterns
- Comprehensive gematria database management
- Seamless workflow from quick calculations to detailed analysis

Author: Assistant
Created: 2024-12-28
Dependencies: PyQt6, loguru, gematria services, document services
"""

import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

from loguru import logger
from PyQt6.QtCore import Qt, pyqtSignal, QSortFilterProxyModel
from PyQt6.QtGui import (
    QAction, QColor, QIcon, QStandardItem, QStandardItemModel,
    QTextCharFormat, QTextCursor
)
from PyQt6.QtWidgets import (
    QApplication, QCheckBox, QColorDialog, QComboBox, QDialog, QFileDialog, QGroupBox, QHBoxLayout, QHeaderView,
    QLabel, QLineEdit, QListWidget, QListWidgetItem, QMenu, QPushButton,
    QProgressBar, QSpinBox, QSplitter, QTabWidget, QTableView, QTextEdit,
    QVBoxLayout, QWidget
)

from document_manager.models.document import Document
from document_manager.models.gematria_dictionary_entry import (
    GematriaDictionaryEntry, GematriaDictionaryResult, VerseAnalysisEntry
)
from document_manager.services.category_service import CategoryService
from document_manager.services.document_service import DocumentService
from document_manager.services.gematria_dictionary_service import GematriaDictionaryService
from document_manager.ui.dialogs.unified_analysis_dialog import UnifiedAnalysisDialog
from gematria.models.calculation_type import CalculationType
from gematria.services.gematria_service import GematriaService
from shared.ui.components.message_box import MessageBox
from shared.ui.widgets.panel import Panel
from shared.ui.widgets.unicode_text_widget import UnicodeTextEdit


@dataclass
class DocumentHighlightState:
    """Represents a saved highlighting state for a document."""
    name: str
    document_id: int
    highlight_colors: Dict[int, Tuple[int, int, int, int]]  # value -> (r, g, b, a)
    active_highlights: Dict[int, List[Tuple[str, int]]]  # value -> [(text, position)]
    search_history: List[Tuple[str, int, str]]  # (search_term, value, method_name)
    text_search_highlights: List[Tuple[str, int]]  # [(text, position)]
    created_date: datetime

    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization."""
        return {
            'name': self.name,
            'document_id': self.document_id,
            'highlight_colors': {str(k): v for k, v in self.highlight_colors.items()},
            'active_highlights': {str(k): v for k, v in self.active_highlights.items()},
            'search_history': self.search_history,
            'text_search_highlights': self.text_search_highlights,
            'created_date': self.created_date.isoformat()
        }

    @classmethod
    def from_dict(cls, data: Dict) -> 'DocumentHighlightState':
        """Create from dictionary for deserialization."""
        return cls(
            name=data['name'],
            document_id=data['document_id'],
            highlight_colors={int(k): v for k, v in data['highlight_colors'].items()},
            active_highlights={int(k): v for k, v in data['active_highlights'].items()},
            search_history=data['search_history'],
            text_search_highlights=data['text_search_highlights'],
            created_date=datetime.fromisoformat(data['created_date'])
        )

# Import TQ analysis service for sending numbers to Quadset Analysis
try:
    from tq.services import tq_analysis_service
    TQ_AVAILABLE = True
except ImportError:
    TQ_AVAILABLE = False

# Import saving dialog from gematria module
try:
    from gematria.ui.dialogs.save_calculation_dialog import SaveCalculationDialog
    SAVE_DIALOG_AVAILABLE = True
except ImportError:
    SAVE_DIALOG_AVAILABLE = False


class GematriaExplorerPanel(Panel):
    """Unified panel for gematria exploration and analysis."""

    # Signals
    gematria_value_calculated = pyqtSignal(str, int)  # (text, value)
    document_content_updated = pyqtSignal(str)  # Document ID
    word_selected = pyqtSignal(str, dict)  # word, gematria_values

    def __init__(self, parent=None):
        """Initialize the Gematria Explorer panel."""
        super().__init__("", parent)  # Empty title to remove header

        # Services
        self.document_service = DocumentService()
        self.gematria_service = GematriaService()
        self.category_service = CategoryService()
        self.dictionary_service = GematriaDictionaryService()

        # Current state
        self.current_document: Optional[Document] = None
        self.current_analysis_result: Optional[GematriaDictionaryResult] = None
        self.current_analysis_text: str = ""

        # Search results storage
        self.text_search_results: List[Tuple[str, int]] = []
        self.value_search_results: List[Tuple[str, int]] = []

        # Multi-color highlighting system
        self.highlight_colors = {}  # Dict[int, QColor] - maps gematria values to colors
        self.active_highlights = {}  # Dict[int, List[Tuple[str, int]]] - maps values to their matches
        self.text_search_highlights = []  # List[Tuple[str, int]] - text search matches
        self.text_search_color = QColor(255, 255, 0, 150)  # Yellow for text searches

        # Performance optimization: word calculation cache
        self.word_value_cache = {}  # Dict[Tuple[str, CalculationType], int] - caches word calculations

        # Search history for color assignment
        self.search_history = []  # List[Tuple[str, int, CalculationType]] - (search_term, value, method)

        # Document state management
        self.document_states = {}  # Dict[int, List[DocumentHighlightState]] - maps doc_id to list of states
        self.current_state_name = None  # Currently loaded state name
        self.color_palette = [
            QColor(255, 255, 0, 100),    # Light Yellow
            QColor(0, 255, 255, 100),    # Light Cyan
            QColor(255, 0, 255, 100),    # Light Magenta
            QColor(255, 165, 0, 100),    # Light Orange
            QColor(144, 238, 144, 100),  # Light Green
            QColor(255, 192, 203, 100),  # Light Pink
            QColor(173, 216, 230, 100),  # Light Blue
            QColor(221, 160, 221, 100),  # Light Plum
            QColor(255, 218, 185, 100),  # Light Peach
            QColor(152, 251, 152, 100),  # Light Pale Green
        ]
        self.next_color_index = 0

        # Track content modification state
        self._content_is_modified = False

        # Data models for analysis tables
        self.word_table_model = QStandardItemModel()
        self.word_proxy_model = QSortFilterProxyModel()
        self.verse_table_model = QStandardItemModel()

        # Initialize UI
        self._init_ui()
        self._setup_models()
        self._connect_signals()

        # Flag to prevent infinite loops during text sync
        self._syncing_text = False

        # Load initial data
        self._refresh_categories()

        logger.debug("GematriaExplorerPanel initialized")

    def _calculate_word_value_cached(self, word: str, method: CalculationType) -> int:
        """Calculate word value with caching for performance."""
        cache_key = (word.lower(), method)

        if cache_key in self.word_value_cache:
            return self.word_value_cache[cache_key]

        try:
            # Handle numbers separately (they don't need gematria calculation)
            numbers = re.findall(r'\b\d+\b', word)
            number_sum = sum(int(num) for num in numbers)
            text_without_numbers = re.sub(r'\b\d+\b', '', word)

            if text_without_numbers.strip():
                text_value = self.gematria_service.calculate(text_without_numbers, method)
            else:
                text_value = 0

            total_value = text_value + number_sum

            # Cache the result
            self.word_value_cache[cache_key] = total_value
            return total_value

        except Exception as e:
            logger.warning(f"Error calculating value for word '{word}': {e}")
            return 0

    def _add_to_search_history(self, search_term: str, value: int, method: CalculationType):
        """Add a search to the history and assign colors based on history."""
        # Check if this value already exists in history
        existing_entry = next((entry for entry in self.search_history if entry[1] == value), None)

        if not existing_entry:
            # New value - add to history
            self.search_history.append((search_term, value, method))
            logger.debug(f"Added to search history: {search_term} = {value}")

        # Assign color based on history position
        self._assign_color_from_history(value)

        # Update search history display
        self._update_search_history_display()

    def _assign_color_from_history(self, value: int):
        """Assign a color to a value based on its position in search history."""
        if value not in self.highlight_colors:
            # Find the position of this value in history
            history_index = next(
                (i for i, entry in enumerate(self.search_history) if entry[1] == value),
                0
            )

            # Assign color based on history position
            color_index = history_index % len(self.color_palette)
            self.highlight_colors[value] = self.color_palette[color_index]

            logger.debug(f"Assigned color {color_index} to value {value} based on history position {history_index}")

    def _get_search_history_summary(self) -> str:
        """Get a summary of recent searches for display."""
        if not self.search_history:
            return "No recent searches"

        recent = self.search_history[-5:]  # Last 5 searches
        summary_lines = []
        for search_term, _, method in recent:
            summary_lines.append(f"{search_term} ({method.name})")

        return "\n".join(summary_lines)

    def _update_search_history_display(self):
        """Update the search history display widget."""
        if hasattr(self, 'search_history_display'):
            history_text = self._get_search_history_summary()
            self.search_history_display.setText(history_text)

    # Document State Management Methods

    def _save_current_state(self):
        """Save the current highlighting state for the document."""
        if not self.current_document:
            MessageBox.warning(self, "Warning", "No document loaded.")
            return

        if not self.active_highlights and not self.text_search_highlights:
            MessageBox.warning(self, "Warning", "No highlights to save.")
            return

        # Get state name from user
        from PyQt6.QtWidgets import QInputDialog
        name, ok = QInputDialog.getText(
            self,
            "Save Highlight State",
            "Enter a name for this highlight state:",
            text=f"State {len(self.document_states.get(self.current_document.id, [])) + 1}"
        )

        if not ok or not name.strip():
            return

        name = name.strip()

        # Check if name already exists
        existing_states = self.document_states.get(self.current_document.id, [])
        if any(state.name == name for state in existing_states):
            reply = MessageBox.question(
                self, "State Exists",
                f"A state named '{name}' already exists. Overwrite it?",
                MessageBox.StandardButton.Yes | MessageBox.StandardButton.No,
                MessageBox.StandardButton.No
            )
            if reply != MessageBox.StandardButton.Yes:
                return

        # Create state object
        state = DocumentHighlightState(
            name=name,
            document_id=self.current_document.id,
            highlight_colors={k: (v.red(), v.green(), v.blue(), v.alpha()) for k, v in self.highlight_colors.items()},
            active_highlights=self.active_highlights.copy(),
            search_history=[(term, value, method.name) for term, value, method in self.search_history],
            text_search_highlights=self.text_search_highlights.copy(),
            created_date=datetime.now()
        )

        # Save state
        if self.current_document.id not in self.document_states:
            self.document_states[self.current_document.id] = []

        # Remove existing state with same name
        self.document_states[self.current_document.id] = [
            s for s in self.document_states[self.current_document.id] if s.name != name
        ]

        # Add new state
        self.document_states[self.current_document.id].append(state)
        self.current_state_name = name

        # Update UI
        self._update_states_list()
        self._update_state_buttons()

        MessageBox.information(self, "State Saved", f"Highlight state '{name}' saved successfully.")
        logger.debug(f"Saved highlight state '{name}' for document {self.current_document.id}")

    def _load_selected_state(self):
        """Load the selected highlighting state."""
        if not self.current_document:
            MessageBox.warning(self, "Warning", "No document loaded.")
            return

        selected_items = self.saved_states_list.selectedItems()
        if not selected_items:
            MessageBox.warning(self, "Warning", "Please select a state to load.")
            return

        state_name = selected_items[0].text().split(' (')[0]  # Remove date part
        states = self.document_states.get(self.current_document.id, [])
        state = next((s for s in states if s.name == state_name), None)

        if not state:
            MessageBox.error(self, "Error", "Selected state not found.")
            return

        # Clear current highlights
        self._clear_all_highlights()

        # Restore state
        self.highlight_colors = {k: QColor(r, g, b, a) for k, (r, g, b, a) in state.highlight_colors.items()}
        self.active_highlights = state.active_highlights.copy()
        self.text_search_highlights = state.text_search_highlights.copy()

        # Restore search history
        self.search_history = []
        for term, value, method_name in state.search_history:
            try:
                method = CalculationType[method_name]
                self.search_history.append((term, value, method))
            except KeyError:
                logger.warning(f"Unknown calculation method: {method_name}")

        # Apply highlights
        self._apply_all_highlights()
        if self.text_search_highlights:
            self._highlight_text_search_results(self.text_search_highlights)

        # Update UI
        self.current_state_name = state_name
        self.clear_highlights_btn.setEnabled(True)
        self._update_color_legend()
        self._update_search_history_display()
        self._update_state_buttons()

        MessageBox.information(self, "State Loaded", f"Highlight state '{state_name}' loaded successfully.")
        logger.debug(f"Loaded highlight state '{state_name}' for document {self.current_document.id}")

    def _rename_selected_state(self):
        """Rename the selected state."""
        if not self.current_document:
            return

        selected_items = self.saved_states_list.selectedItems()
        if not selected_items:
            MessageBox.warning(self, "Warning", "Please select a state to rename.")
            return

        old_name = selected_items[0].text().split(' (')[0]
        states = self.document_states.get(self.current_document.id, [])
        state = next((s for s in states if s.name == old_name), None)

        if not state:
            return

        from PyQt6.QtWidgets import QInputDialog
        new_name, ok = QInputDialog.getText(
            self, "Rename State", "Enter new name:", text=old_name
        )

        if ok and new_name.strip() and new_name.strip() != old_name:
            new_name = new_name.strip()

            # Check if new name already exists
            if any(s.name == new_name for s in states if s != state):
                MessageBox.warning(self, "Warning", f"A state named '{new_name}' already exists.")
                return

            state.name = new_name
            if self.current_state_name == old_name:
                self.current_state_name = new_name

            self._update_states_list()
            MessageBox.information(self, "State Renamed", f"State renamed to '{new_name}'.")

    def _delete_selected_state(self):
        """Delete the selected state."""
        if not self.current_document:
            return

        selected_items = self.saved_states_list.selectedItems()
        if not selected_items:
            MessageBox.warning(self, "Warning", "Please select a state to delete.")
            return

        state_name = selected_items[0].text().split(' (')[0]

        reply = MessageBox.question(
            self, "Delete State",
            f"Are you sure you want to delete the state '{state_name}'?",
            MessageBox.StandardButton.Yes | MessageBox.StandardButton.No,
            MessageBox.StandardButton.No
        )

        if reply == MessageBox.StandardButton.Yes:
            states = self.document_states.get(self.current_document.id, [])
            self.document_states[self.current_document.id] = [
                s for s in states if s.name != state_name
            ]

            if self.current_state_name == state_name:
                self.current_state_name = None

            self._update_states_list()
            self._update_state_buttons()
            MessageBox.information(self, "State Deleted", f"State '{state_name}' deleted.")

    def _on_state_selection_changed(self):
        """Handle state selection changes."""
        self._update_state_buttons()

    def _update_states_list(self):
        """Update the saved states list for the current document."""
        self.saved_states_list.clear()

        if not self.current_document:
            return

        states = self.document_states.get(self.current_document.id, [])
        states.sort(key=lambda s: s.created_date, reverse=True)

        for state in states:
            date_str = state.created_date.strftime("%m/%d %H:%M")
            highlight_count = len(state.active_highlights) + (1 if state.text_search_highlights else 0)
            item_text = f"{state.name} ({highlight_count} highlights, {date_str})"
            self.saved_states_list.addItem(item_text)

    def _update_state_buttons(self):
        """Update the state management button states."""
        has_document = self.current_document is not None
        has_highlights = bool(self.active_highlights or self.text_search_highlights)
        has_selection = bool(self.saved_states_list.selectedItems())
        has_states = self.saved_states_list.count() > 0

        self.save_state_btn.setEnabled(has_document and has_highlights)
        self.load_state_btn.setEnabled(has_document and has_selection)
        self.rename_state_btn.setEnabled(has_document and has_selection)
        self.delete_state_btn.setEnabled(has_document and has_selection)

    def _update_current_state_label(self):
        """Update the current state label."""
        if not self.current_document:
            self.current_state_label.setText("No document loaded")
        elif self.current_state_name:
            self.current_state_label.setText(f"Current: {self.current_state_name}")
        else:
            highlight_count = len(self.active_highlights) + (1 if self.text_search_highlights else 0)
            if highlight_count > 0:
                self.current_state_label.setText(f"Unsaved state ({highlight_count} highlights)")
            else:
                self.current_state_label.setText(f"Document: {self.current_document.name[:20]}...")

    def _init_ui(self):
        """Initialize the user interface."""
        # Clear any default margins
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # Create main tab widget
        self.tab_widget = QTabWidget()
        self.main_layout.addWidget(self.tab_widget)

        # Create tabs
        self._create_interactive_tab()
        self._create_analysis_tab()

    def _create_interactive_tab(self):
        """Create the Interactive Explorer tab."""
        interactive_widget = QWidget()
        self.tab_widget.addTab(interactive_widget, "📖 Interactive Explorer")

        # Main splitter for three-pane layout: tools | content | highlight management
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        interactive_layout = QVBoxLayout(interactive_widget)
        interactive_layout.setContentsMargins(5, 5, 5, 5)
        interactive_layout.addWidget(main_splitter)

        # Left side - Tools panel (document selection, calculator)
        tools_widget = self._create_tools_panel()
        main_splitter.addWidget(tools_widget)

        # Center - Content panel (document display)
        content_widget = self._create_content_panel()
        main_splitter.addWidget(content_widget)

        # Right side - Highlight Management panel (search, history, state management)
        highlight_widget = self._create_highlight_management_panel()
        main_splitter.addWidget(highlight_widget)

        # Set initial sizes (tools: 250px, content: 600px, highlights: 350px)
        main_splitter.setSizes([250, 600, 350])

    def _create_tools_panel(self) -> QWidget:
        """Create the tools panel for the interactive tab."""
        tools_widget = QWidget()
        tools_layout = QVBoxLayout(tools_widget)
        tools_layout.setContentsMargins(5, 5, 5, 5)
        tools_layout.setSpacing(10)

        # Document selection section
        doc_group = QGroupBox("Document Selection")
        doc_layout = QVBoxLayout(doc_group)

        # Category selector
        category_layout = QHBoxLayout()
        category_layout.addWidget(QLabel("Category:"))
        self.category_combo = QComboBox()
        self.category_combo.setMinimumWidth(150)
        category_layout.addWidget(self.category_combo)
        doc_layout.addLayout(category_layout)

        # Document selector
        document_layout = QHBoxLayout()
        document_layout.addWidget(QLabel("Document:"))
        self.document_combo = QComboBox()
        self.document_combo.setMinimumWidth(150)
        document_layout.addWidget(self.document_combo)
        doc_layout.addLayout(document_layout)

        # Load and refresh buttons
        button_layout = QHBoxLayout()
        self.load_btn = QPushButton("Load")
        self.load_btn.setMaximumWidth(60)
        self.refresh_btn = QPushButton("Refresh")
        self.refresh_btn.setMaximumWidth(60)
        button_layout.addWidget(self.load_btn)
        button_layout.addWidget(self.refresh_btn)
        button_layout.addStretch()
        doc_layout.addLayout(button_layout)

        # Document info
        self.doc_title_label = QLabel("No document loaded")
        self.doc_title_label.setStyleSheet("font-weight: bold; font-size: 11px;")
        self.doc_title_label.setWordWrap(True)
        doc_layout.addWidget(self.doc_title_label)

        self.doc_info_label = QLabel("")
        self.doc_info_label.setStyleSheet("font-size: 10px; color: #666;")
        doc_layout.addWidget(self.doc_info_label)

        tools_layout.addWidget(doc_group)

        # Gematria calculation section
        calc_group = QGroupBox("Gematria Calculator")
        calc_layout = QVBoxLayout(calc_group)

        # Method selection
        method_layout = QHBoxLayout()
        method_layout.addWidget(QLabel("Method:"))
        self.method_combo = QComboBox()
        self._populate_calculation_methods()
        method_layout.addWidget(self.method_combo)
        calc_layout.addLayout(method_layout)

        # Calculate button and result
        calc_result_layout = QHBoxLayout()
        self.calc_btn = QPushButton("Calculate Selection")
        self.calc_btn.setEnabled(False)
        calc_result_layout.addWidget(self.calc_btn)

        calc_result_layout.addWidget(QLabel("Value:"))
        self.result_label = QLabel("--")
        self.result_label.setStyleSheet("font-weight: bold; font-size: 12px; color: #2c3e50;")
        calc_result_layout.addWidget(self.result_label)
        calc_layout.addLayout(calc_result_layout)

        tools_layout.addWidget(calc_group)

        # Add stretch to push everything up
        tools_layout.addStretch()

        return tools_widget

    def _create_highlight_management_panel(self) -> QWidget:
        """Create the highlight management panel for the right side."""
        highlight_widget = QWidget()
        highlight_layout = QVBoxLayout(highlight_widget)
        highlight_layout.setContentsMargins(5, 5, 5, 5)
        highlight_layout.setSpacing(10)

        # Search section (moved from left panel)
        search_group = QGroupBox("Search Tools")
        search_layout = QVBoxLayout(search_group)

        # Value search
        value_search_layout = QHBoxLayout()
        value_search_layout.addWidget(QLabel("Find Value:"))
        self.value_search_input = QSpinBox()
        self.value_search_input.setRange(1, 9999)
        self.value_search_input.setValue(26)
        value_search_layout.addWidget(self.value_search_input)

        self.search_value_btn = QPushButton("Search")
        self.search_value_btn.setMaximumWidth(60)
        value_search_layout.addWidget(self.search_value_btn)
        search_layout.addLayout(value_search_layout)

        # Phrase search mode
        self.phrase_search_mode = QCheckBox("Search for phrases")
        self.phrase_search_mode.setToolTip("Search for consecutive words that match the target value")
        search_layout.addWidget(self.phrase_search_mode)

        # Text search
        text_search_layout = QHBoxLayout()
        text_search_layout.addWidget(QLabel("Find Text:"))
        self.text_search_input = QLineEdit()
        self.text_search_input.setPlaceholderText("Search text...")
        text_search_layout.addWidget(self.text_search_input)

        self.search_text_btn = QPushButton("Search")
        self.search_text_btn.setMaximumWidth(60)
        self.search_text_btn.setEnabled(False)
        text_search_layout.addWidget(self.search_text_btn)
        search_layout.addLayout(text_search_layout)

        # Clear highlights button
        clear_highlights_layout = QHBoxLayout()
        clear_highlights_layout.addStretch()
        self.clear_highlights_btn = QPushButton("Clear Highlights")
        self.clear_highlights_btn.setMaximumWidth(120)
        self.clear_highlights_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
                border-radius: 3px;
                border: none;
            }
            QPushButton:hover { background-color: #c0392b; }
            QPushButton:disabled { background-color: #bdc3c7; }
        """)
        self.clear_highlights_btn.setEnabled(False)
        clear_highlights_layout.addWidget(self.clear_highlights_btn)
        clear_highlights_layout.addStretch()
        search_layout.addLayout(clear_highlights_layout)

        # Search progress bar
        self.search_progress_bar = QProgressBar()
        self.search_progress_bar.setVisible(False)
        self.search_progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                text-align: center;
                font-size: 10px;
                height: 16px;
            }
            QProgressBar::chunk {
                background-color: #3498db;
                border-radius: 2px;
            }
        """)
        search_layout.addWidget(self.search_progress_bar)

        # Search results
        results_label = QLabel("Search Results")
        results_label.setStyleSheet("font-weight: bold; font-size: 11px;")
        search_layout.addWidget(results_label)

        self.results_label = QLabel("No search results")
        self.results_label.setStyleSheet("font-size: 10px; color: #666;")
        search_layout.addWidget(self.results_label)

        self.results_list = QListWidget()
        self.results_list.setMaximumHeight(150)
        search_layout.addWidget(self.results_list)

        # Color legend for highlights
        legend_label = QLabel("Highlight Colors")
        legend_label.setStyleSheet("font-weight: bold; font-size: 11px; margin-top: 10px;")
        search_layout.addWidget(legend_label)

        # Instructions for color changing
        color_instructions = QLabel("(Click color squares to change)")
        color_instructions.setStyleSheet("font-size: 9px; color: #888; font-style: italic; margin-bottom: 5px;")
        search_layout.addWidget(color_instructions)

        self.color_legend_widget = QWidget()
        self.color_legend_layout = QVBoxLayout(self.color_legend_widget)
        self.color_legend_layout.setContentsMargins(0, 0, 0, 0)
        self.color_legend_layout.setSpacing(2)

        # Initially empty - will be populated when highlights are added
        no_highlights_label = QLabel("No active highlights")
        no_highlights_label.setStyleSheet("font-size: 10px; color: #666; font-style: italic;")
        self.color_legend_layout.addWidget(no_highlights_label)

        search_layout.addWidget(self.color_legend_widget)

        # Search history section
        history_label = QLabel("Search History")
        history_label.setStyleSheet("font-weight: bold; font-size: 11px; margin-top: 10px;")
        search_layout.addWidget(history_label)

        self.search_history_display = QLabel("No recent searches")
        self.search_history_display.setStyleSheet("font-size: 9px; color: #666; margin-bottom: 5px;")
        self.search_history_display.setWordWrap(True)
        self.search_history_display.setMaximumHeight(60)
        search_layout.addWidget(self.search_history_display)

        highlight_layout.addWidget(search_group)

        # Document State Management section
        state_group = QGroupBox("Document Highlight States")
        state_layout = QVBoxLayout(state_group)

        # Current document state info
        self.current_state_label = QLabel("No document loaded")
        self.current_state_label.setStyleSheet("font-weight: bold; font-size: 11px; color: #2c3e50;")
        state_layout.addWidget(self.current_state_label)

        # State management controls
        state_controls_layout = QHBoxLayout()

        self.save_state_btn = QPushButton("Save State")
        self.save_state_btn.setEnabled(False)
        self.save_state_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
                border-radius: 3px;
                border: none;
            }
            QPushButton:hover { background-color: #229954; }
            QPushButton:disabled { background-color: #bdc3c7; }
        """)
        state_controls_layout.addWidget(self.save_state_btn)

        self.load_state_btn = QPushButton("Load State")
        self.load_state_btn.setEnabled(False)
        self.load_state_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
                border-radius: 3px;
                border: none;
            }
            QPushButton:hover { background-color: #2980b9; }
            QPushButton:disabled { background-color: #bdc3c7; }
        """)
        state_controls_layout.addWidget(self.load_state_btn)

        state_layout.addLayout(state_controls_layout)

        # Saved states list
        states_label = QLabel("Saved States:")
        states_label.setStyleSheet("font-weight: bold; font-size: 10px; margin-top: 10px;")
        state_layout.addWidget(states_label)

        self.saved_states_list = QListWidget()
        self.saved_states_list.setMaximumHeight(120)
        self.saved_states_list.setStyleSheet("""
            QListWidget {
                border: 1px solid #bdc3c7;
                border-radius: 3px;
                background-color: #f8f9fa;
            }
            QListWidget::item {
                padding: 3px;
                border-bottom: 1px solid #dee2e6;
            }
            QListWidget::item:selected {
                background-color: #3498db;
                color: white;
            }
        """)
        state_layout.addWidget(self.saved_states_list)

        # State management buttons
        state_mgmt_layout = QHBoxLayout()

        self.rename_state_btn = QPushButton("Rename")
        self.rename_state_btn.setMaximumWidth(60)
        self.rename_state_btn.setEnabled(False)
        state_mgmt_layout.addWidget(self.rename_state_btn)

        self.delete_state_btn = QPushButton("Delete")
        self.delete_state_btn.setMaximumWidth(60)
        self.delete_state_btn.setEnabled(False)
        self.delete_state_btn.setStyleSheet("QPushButton { background-color: #e74c3c; color: white; }")
        state_mgmt_layout.addWidget(self.delete_state_btn)

        state_mgmt_layout.addStretch()
        state_layout.addLayout(state_mgmt_layout)

        highlight_layout.addWidget(state_group)

        # Add stretch to push everything up
        highlight_layout.addStretch()

        return highlight_widget

    def _create_content_panel(self) -> QWidget:
        """Create the content panel for document display."""
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(5, 5, 5, 5)
        content_layout.setSpacing(5)

        # Document content display
        self.doc_content_display = UnicodeTextEdit()
        self.doc_content_display.setReadOnly(False)
        self.doc_content_display.setAcceptRichText(False)
        self.doc_content_display.setPlaceholderText(
            "Load a document to begin gematria exploration...\n\n"
            "Features:\n"
            "• Select text and click 'Calculate Selection' to get gematria values\n"
            "• Right-click for context menu with quick actions\n"
            "• Use search tools to find words/phrases with specific values\n"
            "• Edit document content and save changes"
        )
        content_layout.addWidget(self.doc_content_display, 1)

        # Save changes button
        self.save_content_btn = QPushButton("Save Text Changes")
        self.save_content_btn.setIcon(QIcon.fromTheme("document-save"))
        self.save_content_btn.setEnabled(False)
        self.save_content_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
                border: none;
            }
            QPushButton:hover {
                background-color: #229954;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        content_layout.addWidget(self.save_content_btn)

        return content_widget

    def _create_analysis_tab(self):
        """Create the Deep Analysis tab."""
        analysis_widget = QWidget()
        self.tab_widget.addTab(analysis_widget, "🔍 Deep Analysis")

        analysis_layout = QVBoxLayout(analysis_widget)
        analysis_layout.setContentsMargins(10, 10, 10, 10)
        analysis_layout.setSpacing(10)

        # Input section
        input_group = QGroupBox("Document Analysis Input")
        input_layout = QVBoxLayout(input_group)

        # Text input area
        self.analysis_text_input = QTextEdit()
        self.analysis_text_input.setPlaceholderText(
            "Paste your document text here for comprehensive analysis...\n\n"
            "For holy books, ensure verse numbers are at the beginning of each line:\n"
            "1 In the beginning was the Word\n"
            "2 And the Word was with God\n"
            "3 And the Word was God\n\n"
            "Or load text from the Interactive Explorer tab."
        )
        self.analysis_text_input.setMaximumHeight(150)
        input_layout.addWidget(self.analysis_text_input)

        # Controls row
        controls_layout = QHBoxLayout()

        # Document title
        controls_layout.addWidget(QLabel("Title:"))
        self.analysis_title_input = QLineEdit()
        self.analysis_title_input.setPlaceholderText("Document title...")
        controls_layout.addWidget(self.analysis_title_input)

        # Language selection
        controls_layout.addWidget(QLabel("Language:"))
        self.analysis_language_combo = QComboBox()
        self.analysis_language_combo.addItems([
            "Auto-detect", "Hebrew", "Greek", "English (TQ)", "Mixed"
        ])
        controls_layout.addWidget(self.analysis_language_combo)

        # Import and analyze buttons
        self.import_text_btn = QPushButton("Import Text File")
        self.import_text_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover { background-color: #2980b9; }
        """)
        controls_layout.addWidget(self.import_text_btn)

        self.analyze_document_btn = QPushButton("Analyze Document")
        self.analyze_document_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover { background-color: #c0392b; }
        """)
        controls_layout.addWidget(self.analyze_document_btn)

        input_layout.addLayout(controls_layout)

        # Progress bar
        self.analysis_progress_bar = QProgressBar()
        self.analysis_progress_bar.setVisible(False)
        input_layout.addWidget(self.analysis_progress_bar)

        analysis_layout.addWidget(input_group)

        # Results section with tabs
        results_group = QGroupBox("Analysis Results")
        results_layout = QVBoxLayout(results_group)

        # Create results tab widget
        self.results_tabs = QTabWidget()

        # Word Dictionary Tab
        self._create_word_dictionary_tab()

        # Verse Analysis Tab
        self._create_verse_analysis_tab()

        results_layout.addWidget(self.results_tabs)

        # Export button
        export_layout = QHBoxLayout()
        export_layout.addStretch()
        self.export_results_btn = QPushButton("Export Results")
        self.export_results_btn.setEnabled(False)
        self.export_results_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 6px 12px;
                border-radius: 4px;
            }
            QPushButton:hover { background-color: #229954; }
            QPushButton:disabled { background-color: #bdc3c7; }
        """)
        export_layout.addWidget(self.export_results_btn)
        results_layout.addLayout(export_layout)

        analysis_layout.addWidget(results_group)

    def _create_word_dictionary_tab(self):
        """Create the Word Dictionary tab within the results."""
        word_dict_widget = QWidget()
        word_dict_layout = QVBoxLayout(word_dict_widget)

        # Statistics
        self.analysis_stats_label = QLabel("No analysis performed yet.")
        self.analysis_stats_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
        word_dict_layout.addWidget(self.analysis_stats_label)

        # Filters
        filters_layout = QHBoxLayout()

        self.word_filter = QLineEdit()
        self.word_filter.setPlaceholderText("Filter by word...")
        filters_layout.addWidget(QLabel("Word:"))
        filters_layout.addWidget(self.word_filter)

        self.language_filter = QComboBox()
        self.language_filter.addItems(["All Languages", "English", "Hebrew", "Greek", "Unknown"])
        filters_layout.addWidget(QLabel("Language:"))
        filters_layout.addWidget(self.language_filter)

        self.value_filter = QSpinBox()
        self.value_filter.setRange(0, 9999)
        self.value_filter.setSpecialValueText("Any Value")
        filters_layout.addWidget(QLabel("Value:"))
        filters_layout.addWidget(self.value_filter)

        self.calc_type_filter = QComboBox()
        self.calc_type_filter.addItem("All Types")
        filters_layout.addWidget(QLabel("Calc Type:"))
        filters_layout.addWidget(self.calc_type_filter)

        self.clear_filters_btn = QPushButton("Clear Filters")
        self.clear_filters_btn.setStyleSheet("QPushButton { background-color: #95a5a6; color: white; }")
        filters_layout.addWidget(self.clear_filters_btn)

        filters_layout.addStretch()
        word_dict_layout.addLayout(filters_layout)

        # Word dictionary table
        self.word_table_view = QTableView()
        self.word_table_view.setSortingEnabled(True)
        self.word_table_view.setAlternatingRowColors(True)
        self.word_table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        word_dict_layout.addWidget(self.word_table_view)

        self.results_tabs.addTab(word_dict_widget, "Word Dictionary")

    def _create_verse_analysis_tab(self):
        """Create the Verse Analysis tab within the results."""
        verse_analysis_widget = QWidget()
        verse_analysis_layout = QVBoxLayout(verse_analysis_widget)

        # Verse analysis statistics
        self.verse_stats_label = QLabel("No verse analysis available.")
        self.verse_stats_label.setStyleSheet("font-weight: bold; color: #2c3e50; padding: 5px;")
        verse_analysis_layout.addWidget(self.verse_stats_label)

        # Instructions and verse saving controls
        controls_layout = QHBoxLayout()

        instructions_label = QLabel("💡 Tip: Double-click on any verse row to see detailed word-by-word breakdown")
        instructions_label.setStyleSheet("color: #6c757d; font-style: italic; padding: 2px;")
        controls_layout.addWidget(instructions_label)

        controls_layout.addStretch()

        # Verse saving buttons
        self.save_selected_verse_btn = QPushButton("Save Selected Verse")
        self.save_selected_verse_btn.setEnabled(False)
        self.save_selected_verse_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
                border-radius: 3px;
                border: none;
            }
            QPushButton:hover { background-color: #2980b9; }
            QPushButton:disabled { background-color: #bdc3c7; }
        """)
        controls_layout.addWidget(self.save_selected_verse_btn)

        self.save_all_verses_btn = QPushButton("Save All Verses")
        self.save_all_verses_btn.setEnabled(False)
        self.save_all_verses_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                padding: 4px 8px;
                border-radius: 3px;
                border: none;
            }
            QPushButton:hover { background-color: #229954; }
            QPushButton:disabled { background-color: #bdc3c7; }
        """)
        controls_layout.addWidget(self.save_all_verses_btn)

        verse_analysis_layout.addLayout(controls_layout)

        # Verse analysis table
        self.verse_table_view = QTableView()
        self.verse_table_view.setModel(self.verse_table_model)
        self.verse_table_view.setSortingEnabled(True)
        self.verse_table_view.setAlternatingRowColors(True)
        self.verse_table_view.setSelectionBehavior(QTableView.SelectionBehavior.SelectRows)
        verse_analysis_layout.addWidget(self.verse_table_view)

        # Global analysis section
        global_analysis_group = QGroupBox("Global Analysis Summary")
        global_analysis_layout = QVBoxLayout(global_analysis_group)

        self.global_analysis_text = QTextEdit()
        self.global_analysis_text.setMaximumHeight(150)
        self.global_analysis_text.setReadOnly(True)
        self.global_analysis_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
                font-family: 'Courier New', monospace;
            }
        """)
        global_analysis_layout.addWidget(self.global_analysis_text)

        verse_analysis_layout.addWidget(global_analysis_group)

        self.results_tabs.addTab(verse_analysis_widget, "Verse Analysis")



    def _populate_calculation_methods(self):
        """Populate the calculation method combo box."""
        self.method_combo.clear()

        # Hebrew methods
        self.method_combo.addItem("Standard (Mispar Hechrachi)", CalculationType.HEBREW_STANDARD_VALUE)
        self.method_combo.addItem("Ordinal (Mispar Siduri)", CalculationType.HEBREW_ORDINAL_VALUE)
        self.method_combo.addItem("Atbash", CalculationType.HEBREW_ATBASH_SUBSTITUTION)
        self.method_combo.addItem("Albam", CalculationType.HEBREW_ALBAM_SUBSTITUTION)

        # Separator
        self.method_combo.insertSeparator(self.method_combo.count())

        # Greek methods
        self.method_combo.addItem("Greek Isopsophy", CalculationType.GREEK_STANDARD_VALUE)
        self.method_combo.addItem("Greek Ordinal", CalculationType.GREEK_ORDINAL_VALUE)
        self.method_combo.addItem("Greek Alpha-Mu", CalculationType.GREEK_ALPHAMU_SUBSTITUTION)
        self.method_combo.addItem("Greek Alpha-Omega", CalculationType.GREEK_ALPHAOMEGA_SUBSTITUTION)

        # Separator
        self.method_combo.insertSeparator(self.method_combo.count())

        # English methods
        self.method_combo.addItem("TQ Method", CalculationType.ENGLISH_TQ_STANDARD_VALUE)

        # Default to TQ Method since documents are typically in English
        tq_index = self.method_combo.findData(CalculationType.ENGLISH_TQ_STANDARD_VALUE)
        if tq_index >= 0:
            self.method_combo.setCurrentIndex(tq_index)

    def _setup_models(self):
        """Set up data models for tables."""
        # Word table proxy model for filtering
        self.word_proxy_model.setSourceModel(self.word_table_model)
        self.word_proxy_model.setFilterCaseSensitivity(Qt.CaseSensitivity.CaseInsensitive)
        self.word_table_view.setModel(self.word_proxy_model)

    def _connect_signals(self):
        """Connect UI signals to their handlers."""
        # Interactive tab signals
        self.category_combo.currentIndexChanged.connect(self._on_category_changed)
        self.load_btn.clicked.connect(self._load_selected_document)
        self.refresh_btn.clicked.connect(self._refresh_categories)
        self.calc_btn.clicked.connect(self._calculate_selection)
        self.search_value_btn.clicked.connect(self._search_by_value)
        self.search_text_btn.clicked.connect(self._search_by_text)
        self.clear_highlights_btn.clicked.connect(self._clear_all_highlights)
        self.save_content_btn.clicked.connect(self._save_document_content_changes)

        # Document content signals
        self.doc_content_display.textChanged.connect(self._handle_content_modification)
        self.doc_content_display.selectionChanged.connect(self._handle_selection_changed)
        self.doc_content_display.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.doc_content_display.customContextMenuRequested.connect(self._show_context_menu)

        # Text search enable/disable
        self.text_search_input.textChanged.connect(
            lambda text: self.search_text_btn.setEnabled(bool(text.strip()))
        )

        # Results list
        self.results_list.itemDoubleClicked.connect(self._on_result_double_clicked)

        # Analysis tab signals
        self.import_text_btn.clicked.connect(self._import_text_file)
        self.analyze_document_btn.clicked.connect(self._analyze_document)
        self.export_results_btn.clicked.connect(self._export_results)

        # Filter signals for word dictionary
        self.word_filter.textChanged.connect(self._apply_word_filters)
        self.language_filter.currentTextChanged.connect(self._apply_word_filters)
        self.value_filter.valueChanged.connect(self._apply_word_filters)
        self.calc_type_filter.currentTextChanged.connect(self._apply_word_filters)
        self.clear_filters_btn.clicked.connect(self._clear_word_filters)

        # Table signals
        self.word_table_view.doubleClicked.connect(self._on_word_double_clicked)
        self.verse_table_view.doubleClicked.connect(self._on_verse_double_clicked)

        # Verse table selection changes
        self.verse_table_view.selectionModel().selectionChanged.connect(self._on_verse_selection_changed)

        # Verse saving buttons
        self.save_selected_verse_btn.clicked.connect(self._save_selected_verse)
        self.save_all_verses_btn.clicked.connect(self._save_all_verses)

        # Text synchronization between tabs
        self.doc_content_display.textChanged.connect(self._on_explorer_text_changed)
        self.analysis_text_input.textChanged.connect(self._on_analysis_text_changed)

        # State management signals
        self.save_state_btn.clicked.connect(self._save_current_state)
        self.load_state_btn.clicked.connect(self._load_selected_state)
        self.rename_state_btn.clicked.connect(self._rename_selected_state)
        self.delete_state_btn.clicked.connect(self._delete_selected_state)
        self.saved_states_list.itemSelectionChanged.connect(self._on_state_selection_changed)
        self.saved_states_list.itemDoubleClicked.connect(self._load_selected_state)

    def _refresh_categories(self):
        """Refresh the categories and document list."""
        # Save current selections
        current_category_id = self.category_combo.currentData() if self.category_combo.count() > 0 else None
        current_doc_id = self.document_combo.currentData() if self.document_combo.count() > 0 else None

        # Clear comboboxes
        self.category_combo.clear()
        self.document_combo.clear()

        # Add "All Documents" option
        self.category_combo.addItem("All Documents", None)

        # Get and add categories
        categories = self.category_service.get_all_categories()
        categories.sort(key=lambda cat: cat.name)

        for category in categories:
            self.category_combo.addItem(category.name, category.id)

        # Restore category selection
        if current_category_id:
            index = self.category_combo.findData(current_category_id)
            if index >= 0:
                self.category_combo.setCurrentIndex(index)
            else:
                self.category_combo.setCurrentIndex(0)

        # Load documents for selected category
        self._refresh_document_list()

        # Restore document selection
        if current_doc_id:
            index = self.document_combo.findData(current_doc_id)
            if index >= 0:
                self.document_combo.setCurrentIndex(index)

    def _on_category_changed(self, _):
        """Handle category selection change."""
        self._refresh_document_list()
        if self.document_combo.count() > 0:
            self.document_combo.setCurrentIndex(0)

    def _refresh_document_list(self):
        """Refresh the document list based on selected category."""
        current_id = self.document_combo.currentData()
        self.document_combo.clear()

        category_id = self.category_combo.currentData()

        if category_id is None:
            documents = self.document_service.get_all_documents()
        else:
            documents = self.document_service.get_documents_by_category(category_id)

        documents.sort(key=lambda doc: doc.name)

        for document in documents:
            self.document_combo.addItem(document.name, document.id)

        if current_id:
            index = self.document_combo.findData(current_id)
            if index >= 0:
                self.document_combo.setCurrentIndex(index)

    # Interactive Explorer Methods

    def _load_selected_document(self):
        """Load the selected document into the content display."""
        document_id = self.document_combo.currentData()
        if not document_id:
            return

        try:
            document = self.document_service.get_document(document_id)
            if not document:
                MessageBox.warning(self, "Warning", "Document not found.")
                return

            self.current_document = document

            # Update document info
            self.doc_title_label.setText(document.name)
            self.doc_info_label.setText(
                f"Size: {document.get_file_size_display()} | "
                f"Type: {document.file_type.value} | "
                f"Modified: {document.last_modified_date.strftime('%Y-%m-%d')}"
            )

            # Load content
            content = document.extracted_text or document.content or ""
            if not content and document.file_path:
                # Try to extract text if not already done
                self.document_service.extract_text(document)
                content = document.extracted_text or ""

            self.doc_content_display.setPlainText(content)
            self._content_is_modified = False
            self.save_content_btn.setEnabled(False)

            # Clear search results and highlights
            self._clear_search_results()
            self._clear_all_highlights()

            # Update state management UI
            self.current_state_name = None
            self._update_states_list()
            self._update_state_buttons()
            self._update_current_state_label()

            logger.debug(f"Loaded document: {document.name}")

        except Exception as e:
            logger.error(f"Error loading document: {e}")
            MessageBox.error(self, "Error", f"Failed to load document: {str(e)}")

    def _handle_content_modification(self):
        """Handle content modification in the document display."""
        if self.current_document:
            self._content_is_modified = True
            self.save_content_btn.setEnabled(True)

    def _handle_selection_changed(self):
        """Handle text selection changes in the document display."""
        cursor = self.doc_content_display.textCursor()
        has_selection = cursor.hasSelection()
        self.calc_btn.setEnabled(has_selection)

        if has_selection:
            # Auto-calculate if selection is reasonable length
            selected_text = cursor.selectedText()
            if len(selected_text) <= 5000:  # Much more generous limit for auto-calculation
                self._calculate_selection(show_result=False)

    def _calculate_selection(self, show_result=True):
        """Calculate gematria value of selected text."""
        cursor = self.doc_content_display.textCursor()
        if not cursor.hasSelection():
            if show_result:
                MessageBox.information(self, "Info", "Please select some text first.")
            return

        selected_text = cursor.selectedText()
        method = self.method_combo.currentData()

        try:
            # Handle numbers in text (face value)
            numbers = re.findall(r'\b\d+\b', selected_text)
            number_sum = sum(int(num) for num in numbers)

            # Remove numbers for gematria calculation
            text_without_numbers = re.sub(r'\b\d+\b', '', selected_text)

            # Calculate gematria value for text
            if text_without_numbers.strip():
                text_value = self.gematria_service.calculate(text_without_numbers, method)
            else:
                text_value = 0

            # Total value
            value = text_value + number_sum

            # Update result label
            self.result_label.setText(str(value))

            # Show detailed result if requested
            if show_result:
                if number_sum > 0:
                    MessageBox.information(
                        self, "Gematria Value",
                        f"The gematria value of '{selected_text}' is {value}\n\n"
                        f"Breakdown:\n"
                        f"- Text value: {text_value}\n"
                        f"- Numbers face value: {number_sum}\n"
                        f"- Total: {value}"
                    )
                else:
                    MessageBox.information(
                        self, "Gematria Value",
                        f"The gematria value of '{selected_text}' is {value}"
                    )

            # Emit signal
            self.gematria_value_calculated.emit(selected_text, value)

        except Exception as e:
            logger.error(f"Error calculating gematria: {e}")
            self.result_label.setText("Error")
            if show_result:
                MessageBox.error(self, "Calculation Error", f"Error calculating gematria value: {str(e)}")

    def _search_by_value(self):
        """Search for words/phrases with a specific gematria value."""
        if not self.current_document or not self.doc_content_display.toPlainText():
            MessageBox.warning(self, "Warning", "Please load a document first.")
            return

        target_value = self.value_search_input.value()
        method = self.method_combo.currentData()

        # Show progress bar
        self.search_progress_bar.setVisible(True)
        self.search_progress_bar.setRange(0, 0)  # Indeterminate progress
        self.search_progress_bar.setFormat("Searching...")

        # Disable search buttons during search
        self.search_value_btn.setEnabled(False)
        self.search_text_btn.setEnabled(False)

        try:
            if self.phrase_search_mode.isChecked():
                self._search_by_phrase_value(target_value, method)
            else:
                self._search_by_word_value(target_value, method)
        finally:
            # Hide progress bar and re-enable buttons
            self.search_progress_bar.setVisible(False)
            self.search_value_btn.setEnabled(True)
            self.search_text_btn.setEnabled(bool(self.text_search_input.text().strip()))

    def _search_by_word_value(self, target_value: int, method: CalculationType):
        """Search for individual words with the target value."""
        content = self.doc_content_display.toPlainText()
        words = re.findall(r'\b\w+\b', content)
        matches = []

        # Update progress bar for determinate progress
        self.search_progress_bar.setRange(0, len(words))
        self.search_progress_bar.setFormat(f"Checking words... (%v/{len(words)})")

        for i, word in enumerate(words):
            # Update progress
            self.search_progress_bar.setValue(i)

            # Process events to keep UI responsive
            if i % 100 == 0:  # Update every 100 words to avoid too much overhead
                QApplication.processEvents()
            try:
                # Use cached calculation for performance
                value = self._calculate_word_value_cached(word, method)

                if value == target_value:
                    # Find all positions of this word
                    for match in re.finditer(r'\b' + re.escape(word) + r'\b', content):
                        matches.append((word, match.start()))

            except Exception:
                continue

        # Add to search history and assign color based on history
        self._add_to_search_history(f"Words = {target_value}", target_value, method)
        self._display_search_results(matches, f"Words with value {target_value}")

    def _search_by_phrase_value(self, target_value: int, method: CalculationType):
        """Search for consecutive word phrases with the target value."""
        content = self.doc_content_display.toPlainText()
        words = re.findall(r'\b\w+\b', content)
        matches = []

        # Calculate total combinations for progress tracking
        total_combinations = sum(len(words) - phrase_length + 1 for phrase_length in range(2, min(11, len(words) + 1)))
        current_combination = 0

        # Update progress bar
        self.search_progress_bar.setRange(0, total_combinations)
        self.search_progress_bar.setFormat(f"Checking phrases... (%v/{total_combinations})")

        # Try phrases of different lengths (2-50 words)
        for phrase_length in range(2, min(51, len(words) + 1)):
            for i in range(len(words) - phrase_length + 1):
                # Update progress
                current_combination += 1
                if current_combination % 50 == 0:  # Update every 50 combinations
                    self.search_progress_bar.setValue(current_combination)
                    QApplication.processEvents()
                phrase_words = words[i:i + phrase_length]
                phrase = ' '.join(phrase_words)

                try:
                    # Use cached calculation for performance
                    value = self._calculate_word_value_cached(phrase, method)

                    if value == target_value:
                        # Find position of this phrase
                        phrase_pattern = r'\b' + r'\s+'.join(re.escape(word) for word in phrase_words) + r'\b'
                        for match in re.finditer(phrase_pattern, content):
                            matches.append((phrase, match.start()))

                except Exception:
                    continue

        # Add to search history and assign color based on history
        self._add_to_search_history(f"Phrases = {target_value}", target_value, method)
        self._display_search_results(matches, f"Phrases with value {target_value}")

    def _search_by_text(self):
        """Search for specific text in the document."""
        search_text = self.text_search_input.text().strip()
        if not search_text:
            return

        # Show progress bar
        self.search_progress_bar.setVisible(True)
        self.search_progress_bar.setRange(0, 0)  # Indeterminate progress
        self.search_progress_bar.setFormat("Searching text...")

        # Disable search buttons during search
        self.search_value_btn.setEnabled(False)
        self.search_text_btn.setEnabled(False)

        try:
            content = self.doc_content_display.toPlainText()
            matches = []

            # Case-insensitive search
            for match in re.finditer(re.escape(search_text), content, re.IGNORECASE):
                matches.append((search_text, match.start()))

            self._display_text_search_results(matches, f"Text matches for '{search_text}'")
        finally:
            # Hide progress bar and re-enable buttons
            self.search_progress_bar.setVisible(False)
            self.search_value_btn.setEnabled(True)
            self.search_text_btn.setEnabled(True)

    def _display_search_results(self, matches: List[Tuple[str, int]], _: str):
        """Display gematria value search results in the results list."""
        self.results_list.clear()
        self.value_search_results = matches

        if not matches:
            self.results_label.setText("No matches found")
            return

        self.results_label.setText(f"{len(matches)} matches found")

        for text, position in matches:
            item = QListWidgetItem(f"{text}: position {position}")
            item.setData(Qt.ItemDataRole.UserRole, position)
            self.results_list.addItem(item)

        # Highlight matches in document with color coding
        self._highlight_search_results(matches)

    def _display_text_search_results(self, matches: List[Tuple[str, int]], _: str):
        """Display text search results in the results list."""
        self.results_list.clear()
        self.text_search_results = matches

        if not matches:
            self.results_label.setText("No matches found")
            return

        self.results_label.setText(f"{len(matches)} matches found")

        for text, position in matches:
            item = QListWidgetItem(f"{text}: position {position}")
            item.setData(Qt.ItemDataRole.UserRole, position)
            self.results_list.addItem(item)

        # Store text search highlights and update legend
        self.text_search_highlights = matches

        # For text searches, use a simple highlight without color coding
        self._highlight_text_search_results(matches)

        # Enable clear highlights button and update legend if we have any highlights
        if matches:
            self.clear_highlights_btn.setEnabled(True)
            self._update_color_legend()

    def _highlight_search_results(self, matches: List[Tuple[str, int]]):
        """Highlight search results in the document display with color-coded values."""
        if not matches:
            return

        # Determine the gematria value for these matches
        target_value = self.value_search_input.value()

        # Color should already be assigned by history system, but ensure it exists
        if target_value not in self.highlight_colors:
            self._assign_color_from_history(target_value)

        # Update matches for this value
        self.active_highlights[target_value] = matches

        # Apply all active highlights (re-render everything to handle overlaps)
        self._apply_all_highlights()

        # Update UI
        self.clear_highlights_btn.setEnabled(True)
        self._update_color_legend()
        self._update_state_buttons()
        self._update_current_state_label()

        # Mark state as modified if we had a saved state
        if self.current_state_name:
            self.current_state_name = None

    def _highlight_text_search_results(self, matches: List[Tuple[str, int]]):
        """Highlight text search results with a simple yellow highlight."""
        if not matches:
            return

        # First apply any existing gematria highlights
        self._apply_all_highlights()

        # Then add text search highlights on top
        cursor = self.doc_content_display.textCursor()
        highlight_format = QTextCharFormat()
        highlight_format.setBackground(QColor(255, 255, 0, 150))  # Slightly more opaque yellow

        # Apply text search highlights
        for text, position in matches:
            try:
                cursor.setPosition(position)
                cursor.movePosition(QTextCursor.MoveOperation.Right, QTextCursor.MoveMode.KeepAnchor, len(text))
                cursor.setCharFormat(highlight_format)
            except Exception as e:
                logger.warning(f"Error applying text highlight at position {position}: {e}")
                continue

    def _apply_all_highlights(self):
        """Apply all active highlights to the document."""
        # First clear all formatting
        cursor = self.doc_content_display.textCursor()
        cursor.select(QTextCursor.SelectionType.Document)
        cursor.setCharFormat(QTextCharFormat())

        # Apply each highlight group
        for value, matches in self.active_highlights.items():
            if value in self.highlight_colors and matches:
                color = self.highlight_colors[value]
                highlight_format = QTextCharFormat()
                highlight_format.setBackground(color)

                for text, position in matches:
                    try:
                        cursor.setPosition(position)
                        cursor.movePosition(QTextCursor.MoveOperation.Right, QTextCursor.MoveMode.KeepAnchor, len(text))
                        cursor.setCharFormat(highlight_format)
                    except Exception as e:
                        logger.warning(f"Error applying highlight at position {position}: {e}")
                        continue

    def _clear_search_results(self):
        """Clear search results but keep existing highlights."""
        self.results_list.clear()
        self.results_label.setText("No search results")
        self.value_search_results = []
        self.text_search_results = []

    def _clear_all_highlights(self):
        """Clear all highlights and reset the highlighting system."""
        # Clear all highlight data
        self.highlight_colors.clear()
        self.active_highlights.clear()
        self.text_search_highlights.clear()
        self.search_history.clear()
        self.next_color_index = 0

        # Clear visual highlights in document
        cursor = self.doc_content_display.textCursor()
        cursor.select(QTextCursor.SelectionType.Document)
        cursor.setCharFormat(QTextCharFormat())  # Reset formatting

        # Update UI
        self.clear_highlights_btn.setEnabled(False)
        self._update_color_legend()
        self._update_search_history_display()
        self._update_state_buttons()
        self._update_current_state_label()

        # Clear current state name
        self.current_state_name = None

        logger.debug("All highlights cleared")

    def _get_next_color(self) -> QColor:
        """Get the next color from the palette."""
        color = self.color_palette[self.next_color_index % len(self.color_palette)]
        self.next_color_index += 1
        return color

    def _update_color_legend(self):
        """Update the color legend to show active highlights."""
        # Clear existing legend items
        for i in reversed(range(self.color_legend_layout.count())):
            child = self.color_legend_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        if not self.active_highlights and not self.text_search_highlights:
            # Show "no highlights" message
            no_highlights_label = QLabel("No active highlights")
            no_highlights_label.setStyleSheet("font-size: 10px; color: #666; font-style: italic;")
            self.color_legend_layout.addWidget(no_highlights_label)
        else:
            # Show text search highlights first if they exist
            if self.text_search_highlights:
                self._add_text_search_legend_item()

            # Then show gematria value highlights
            # Show legend for each active highlight
            for value in sorted(self.active_highlights.keys()):
                if value in self.highlight_colors:
                    color = self.highlight_colors[value]
                    match_count = len(self.active_highlights[value])

                    # Create legend item
                    legend_item = QWidget()
                    legend_layout = QHBoxLayout(legend_item)
                    legend_layout.setContentsMargins(0, 0, 0, 0)
                    legend_layout.setSpacing(5)

                    # Clickable color swatch button
                    color_swatch = QPushButton()
                    color_swatch.setFixedSize(20, 20)
                    color_swatch.setToolTip(f"Click to change color for value {value}")
                    color_swatch.setStyleSheet(f"""
                        QPushButton {{
                            background-color: rgba({color.red()}, {color.green()}, {color.blue()}, {color.alpha()});
                            border: 2px solid #666;
                            border-radius: 3px;
                        }}
                        QPushButton:hover {{
                            border: 2px solid #333;
                        }}
                        QPushButton:pressed {{
                            border: 2px solid #000;
                        }}
                    """)
                    # Connect to color picker (fix lambda closure issue)
                    color_swatch.clicked.connect(lambda checked, v=value: self._change_highlight_color(v))
                    legend_layout.addWidget(color_swatch)

                    # Value and count label
                    value_label = QLabel(f"Value {value} ({match_count} matches)")
                    value_label.setStyleSheet("font-size: 10px;")
                    legend_layout.addWidget(value_label)

                    legend_layout.addStretch()
                    self.color_legend_layout.addWidget(legend_item)

    def _change_highlight_color(self, value: int):
        """Open color picker to change the highlight color for a specific gematria value."""
        if value not in self.highlight_colors:
            return

        current_color = self.highlight_colors[value]

        # Open color picker dialog
        new_color = QColorDialog.getColor(
            current_color,
            self,
            f"Choose Highlight Color for Value {value}",
            QColorDialog.ColorDialogOption.ShowAlphaChannel
        )

        # If user selected a valid color
        if new_color.isValid():
            # Update the color
            self.highlight_colors[value] = new_color

            # Re-apply all highlights with the new color
            self._apply_all_highlights()

            # Update the color legend to show the new color
            self._update_color_legend()

            logger.debug(f"Changed highlight color for value {value} to {new_color.name()}")

    def _add_text_search_legend_item(self):
        """Add a legend item for text search highlights."""
        match_count = len(self.text_search_highlights)

        # Create legend item
        legend_item = QWidget()
        legend_layout = QHBoxLayout(legend_item)
        legend_layout.setContentsMargins(0, 0, 0, 0)
        legend_layout.setSpacing(5)

        # Color swatch (non-clickable for text searches)
        color_swatch = QLabel()
        color_swatch.setFixedSize(20, 20)
        color_swatch.setToolTip("Text search highlight (fixed color)")
        color = self.text_search_color
        color_swatch.setStyleSheet(f"""
            QLabel {{
                background-color: rgba({color.red()}, {color.green()}, {color.blue()}, {color.alpha()});
                border: 2px solid #666;
                border-radius: 3px;
            }}
        """)
        legend_layout.addWidget(color_swatch)

        # Label
        value_label = QLabel(f"Text Search ({match_count} matches)")
        value_label.setStyleSheet("font-size: 10px;")
        legend_layout.addWidget(value_label)

        legend_layout.addStretch()
        self.color_legend_layout.addWidget(legend_item)

    def _on_result_double_clicked(self, item):
        """Handle double-click on search result item."""
        position = item.data(Qt.ItemDataRole.UserRole)
        if position is not None:
            cursor = self.doc_content_display.textCursor()
            cursor.setPosition(position)

            # Select the found text
            text = item.text().split(":")[0]
            cursor.movePosition(
                QTextCursor.MoveOperation.Right,
                QTextCursor.MoveMode.KeepAnchor,
                len(text)
            )

            self.doc_content_display.setTextCursor(cursor)
            self.doc_content_display.setFocus()

    def _save_document_content_changes(self):
        """Save changes to the document content."""
        if not self.current_document or not self._content_is_modified:
            return

        try:
            # Update document content
            new_content = self.doc_content_display.toPlainText()
            self.current_document.extracted_text = new_content
            self.current_document.last_modified_date = datetime.now()

            # Save to database
            success = self.document_service.save_document(self.current_document)

            if success:
                self._content_is_modified = False
                self.save_content_btn.setEnabled(False)
                MessageBox.information(self, "Success", "Document changes saved successfully.")

                # Emit signal
                self.document_content_updated.emit(self.current_document.id)
            else:
                MessageBox.error(self, "Error", "Failed to save document changes.")

        except Exception as e:
            logger.error(f"Error saving document changes: {e}")
            MessageBox.error(self, "Error", f"Failed to save changes: {str(e)}")

    def _show_context_menu(self, position):
        """Show context menu for document content."""
        cursor = self.doc_content_display.textCursor()
        menu = QMenu(self)

        # If text is selected, add gematria actions
        if cursor.hasSelection():
            selected_text = cursor.selectedText()

            # Calculate current value
            method = self.method_combo.currentData()
            try:
                value = self.gematria_service.calculate(selected_text, method)

                # Add action to search for this value
                search_action = QAction(f"Find Words with Value {value}", self)
                search_action.triggered.connect(lambda: self._search_for_value(value))
                menu.addAction(search_action)

                # Add action to save to database
                save_action = QAction("Save to Gematria Database", self)
                save_action.triggered.connect(lambda: self._save_to_database(selected_text, value))
                menu.addAction(save_action)

                # Add TQ analysis action if available
                if TQ_AVAILABLE:
                    tq_action = QAction("Send to TQ Analysis", self)
                    tq_action.triggered.connect(lambda: self._send_to_tq_analysis(value))
                    menu.addAction(tq_action)

            except Exception as e:
                logger.warning(f"Error calculating value for context menu: {e}")

        # Add document-level actions
        if self.current_document and self.current_document.metadata.get("has_greek_text"):
            revert_action = QAction("Revert Greek Text Conversion", self)
            revert_action.triggered.connect(self._revert_greek_conversion)
            menu.addAction(revert_action)

        # Show menu if it has actions
        if not menu.isEmpty():
            menu.exec(self.doc_content_display.viewport().mapToGlobal(position))

    def _search_for_value(self, value: int):
        """Search for words with the specified gematria value."""
        self.value_search_input.setValue(value)
        self._search_by_value()

    def _save_to_database(self, text: str, value: int):
        """Save calculation to gematria database using Word Abacus-style dialog."""
        if not SAVE_DIALOG_AVAILABLE:
            # Fallback to simple save if dialog not available
            self._simple_save_to_database(text, value)
            return

        try:
            # Get current calculation method
            method = self.method_combo.currentData()
            method_name = getattr(method, 'display_name', str(method))

            # Create and show the save dialog (same as Word Abacus)
            save_dialog = SaveCalculationDialog(value, text, method_name, self)

            if save_dialog.exec() == QDialog.DialogCode.Accepted:
                # Get the data from the dialog
                tags = save_dialog.selected_tags
                notes = save_dialog.notes
                favorite = save_dialog.is_favorite

                # Handle numbers in text for enhanced notes
                numbers = re.findall(r'\b\d+\b', text)
                number_sum = sum(int(num) for num in numbers)

                enhanced_notes = notes
                if number_sum > 0:
                    text_value = value - number_sum
                    number_note = (
                        f"Numbers face value: {number_sum}, Text value: {text_value}. "
                        f"Numbers in text are calculated at their face value."
                    )
                    enhanced_notes = f"{notes}\n{number_note}" if notes else number_note

                # Save to database using gematria service
                self.gematria_service.calculate_and_save(
                    text=text,
                    calculation_type=method,
                    notes=enhanced_notes,
                    tags=tags,
                    favorite=favorite,
                    value=value
                )

                logger.debug(f"Saved calculation from Gematria Explorer: {text} = {value}")
                MessageBox.information(
                    self, "Saved to Database",
                    f"'{text}' (value: {value}) has been saved to the gematria database."
                )

        except Exception as e:
            logger.error(f"Error saving to database: {e}")
            MessageBox.error(self, "Error", f"Failed to save to database: {str(e)}")

    def _simple_save_to_database(self, text: str, value: int):
        """Simple fallback save method if SaveCalculationDialog is not available."""
        try:
            method = self.method_combo.currentData()

            # Handle numbers in text for notes
            numbers = re.findall(r'\b\d+\b', text)
            number_sum = sum(int(num) for num in numbers)

            notes = None
            if number_sum > 0:
                text_value = value - number_sum
                notes = (
                    f"Numbers face value: {number_sum}, Text value: {text_value}. "
                    f"Numbers in text are calculated at their face value."
                )

            # Save to database
            self.gematria_service.calculate_and_save(text, method, value=value, notes=notes)

            MessageBox.information(
                self, "Saved to Database",
                f"The calculation for '{text}' with value {value} has been saved to the database."
            )

        except Exception as e:
            logger.error(f"Error saving to database: {e}")
            MessageBox.error(self, "Error", f"Failed to save to database: {str(e)}")

    def _send_to_tq_analysis(self, value: int):
        """Send value to TQ analysis if available."""
        if TQ_AVAILABLE:
            try:
                # Open the TQ Grid with this number
                panel = tq_analysis_service.get_instance().open_quadset_analysis(value)

                # Find the window containing this panel and ensure it's on top
                parent = panel.window()
                if parent and hasattr(parent, "ensure_on_top"):
                    # Use a timer to ensure the window appears on top after creation
                    from PyQt6.QtCore import QTimer
                    QTimer.singleShot(100, parent.ensure_on_top)

                MessageBox.information(
                    self, "Sent to TQ",
                    f"Value {value} has been sent to TQ Quadset Analysis."
                )
            except Exception as e:
                logger.error(f"Error sending to TQ analysis: {e}")
                MessageBox.error(self, "Error", f"Failed to send to TQ analysis: {str(e)}")

    def _revert_greek_conversion(self):
        """Revert Greek text conversion if applicable."""
        # This would implement Greek text reversion logic
        # For now, just show a placeholder message
        MessageBox.information(
            self, "Greek Text Reversion",
            "Greek text reversion functionality will be implemented in a future update."
        )

    # Deep Analysis Methods

    def _clear_analysis_results(self):
        """Clear all analysis results and reset the UI."""
        # Clear analysis result data
        self.current_analysis_result = None
        self.current_analysis_text = None

        # Clear tables
        self.word_table_model.clear()
        self.verse_table_model.clear()

        # Reset statistics labels
        self.analysis_stats_label.setText("No analysis performed yet.")
        self.verse_stats_label.setText("No verse analysis available.")

        # Clear global analysis
        self.global_analysis_text.clear()

        # Disable buttons
        self.export_results_btn.setEnabled(False)
        self.save_selected_verse_btn.setEnabled(False)
        self.save_all_verses_btn.setEnabled(False)

        # Clear filters
        self.calc_type_filter.clear()
        self.calc_type_filter.addItem("All Types")

    def _import_text_file(self):
        """Import text from a file for analysis."""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "Import Text File",
            "", "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                # Try different encodings to handle various file types
                encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']
                content = None

                for encoding in encodings:
                    try:
                        with open(file_path, 'r', encoding=encoding) as file:
                            content = file.read()
                        logger.debug(f"Successfully read file with {encoding} encoding")
                        break
                    except UnicodeDecodeError:
                        continue

                if content is None:
                    # Last resort: read as binary and decode with error handling
                    with open(file_path, 'rb') as file:
                        raw_content = file.read()
                    content = raw_content.decode('utf-8', errors='replace')
                    logger.warning(f"Used fallback encoding with error replacement for {file_path}")

                self.analysis_text_input.setPlainText(content)

                # Set title from filename
                filename = Path(file_path).stem
                self.analysis_title_input.setText(filename)

                # Clear previous analysis results since we have new text
                self._clear_analysis_results()

                # Ask user if they want to analyze immediately
                reply = MessageBox.question(
                    self, "Auto-Analyze",
                    f"Text imported from {Path(file_path).name}\n\n"
                    f"Would you like to analyze this document now?",
                    MessageBox.StandardButton.Yes | MessageBox.StandardButton.No,
                    MessageBox.StandardButton.Yes
                )

                if reply == MessageBox.StandardButton.Yes:
                    self._analyze_document()
                else:
                    MessageBox.information(self, "Import Complete",
                                         f"Text imported successfully. Click 'Analyze Document' when ready.")

            except Exception as e:
                logger.error(f"Error importing text file: {e}")
                MessageBox.error(self, "Error", f"Failed to import text file: {str(e)}")

    def _analyze_document(self):
        """Perform comprehensive gematria analysis on the document."""
        text = self.analysis_text_input.toPlainText().strip()
        if not text:
            MessageBox.warning(self, "Warning", "Please enter or import text to analyze.")
            return

        title = self.analysis_title_input.text().strip() or "Untitled Document"

        # FIRST: Clear all previous analysis results completely
        self._clear_analysis_results()

        # Show progress
        self.analysis_progress_bar.setVisible(True)
        self.analysis_progress_bar.setRange(0, 0)  # Indeterminate
        self.analyze_document_btn.setEnabled(False)

        try:
            # Perform analysis using the dictionary service
            result = self.dictionary_service.analyze_document(
                text=text,
                document_title=title
            )

            if result:
                self.current_analysis_result = result
                self.current_analysis_text = text
                self._display_analysis_results(result)
                self.export_results_btn.setEnabled(True)

                MessageBox.information(
                    self, "Analysis Complete",
                    f"Analysis completed successfully!\n\n"
                    f"Words analyzed: {len(result.entries)}\n"
                    f"Verses analyzed: {len(result.verse_analysis) if result.verse_analysis else 0}"
                )
            else:
                MessageBox.error(self, "Error", "Analysis failed to produce results.")

        except Exception as e:
            logger.error(f"Error during document analysis: {e}")
            MessageBox.error(self, "Analysis Error", f"Failed to analyze document: {str(e)}")

        finally:
            # Hide progress
            self.analysis_progress_bar.setVisible(False)
            self.analyze_document_btn.setEnabled(True)

    def _display_analysis_results(self, result: GematriaDictionaryResult):
        """Display the analysis results in the tables."""
        # Update statistics
        word_count = len(result.entries)
        verse_count = len(result.verse_analysis) if result.verse_analysis else 0

        self.analysis_stats_label.setText(
            f"Analysis Results: {word_count} unique words, {verse_count} verses analyzed"
        )

        # Populate word dictionary table
        self._populate_word_dictionary_table(result.entries)

        # ALWAYS clear verse table first, then populate if we have data
        self.verse_table_model.clear()

        # Populate verse analysis table if available
        if result.verse_analysis:
            self._populate_verse_analysis_table(result.verse_analysis)
            self.verse_stats_label.setText(f"Verse Analysis: {verse_count} verses")
            # Enable verse saving buttons
            self.save_all_verses_btn.setEnabled(True)
        else:
            # Ensure verse table stays empty and show appropriate message
            self.verse_stats_label.setText("No verse analysis available (text not in verse format)")
            # Disable verse saving buttons
            self.save_selected_verse_btn.setEnabled(False)
            self.save_all_verses_btn.setEnabled(False)

        # Display global analysis
        if result.global_analysis:
            # Format the global analysis summary
            global_text = self._format_global_analysis(result.global_analysis)
            self.global_analysis_text.setPlainText(global_text)
        else:
            self.global_analysis_text.setPlainText("No global analysis available.")

        # Populate calculation type filter
        self._populate_calc_type_filter(result.entries)

    def _populate_word_dictionary_table(self, entries: List[GematriaDictionaryEntry]):
        """Populate the word dictionary table with analysis results."""
        self.word_table_model.clear()

        # Set headers
        headers = ["Word", "Language", "Frequency", "Verses", "Gematria Values"]
        self.word_table_model.setHorizontalHeaderLabels(headers)

        # Add data
        for entry in entries:
            row = []

            # Word
            word_item = QStandardItem(entry.word)
            row.append(word_item)

            # Language
            language_item = QStandardItem(entry.language.value)
            row.append(language_item)

            # Frequency
            frequency_item = QStandardItem(str(entry.frequency))
            frequency_item.setData(entry.frequency, Qt.ItemDataRole.UserRole)
            row.append(frequency_item)

            # Verses
            verses_text = ", ".join(map(str, sorted(entry.verse_numbers))) if entry.verse_numbers else "N/A"
            verses_item = QStandardItem(verses_text)
            row.append(verses_item)

            # Gematria Values (show all calculation types)
            values_text = []
            for calc_type, value in entry.gematria_values.items():
                values_text.append(f"{calc_type}: {value}")
            gematria_item = QStandardItem("; ".join(values_text))
            row.append(gematria_item)

            self.word_table_model.appendRow(row)

        # Resize columns
        header = self.word_table_view.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Word
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Language
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # Frequency
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.Stretch)  # Calculation Type
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Value
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.Stretch)  # Notes

    def _populate_verse_analysis_table(self, verse_analysis: List[VerseAnalysisEntry]):
        """Populate the verse analysis table."""
        self.verse_table_model.clear()

        # Set headers
        headers = ["Verse", "Chapter", "Text", "Words", "Numbers", "Gematria Total", "Verse Total"]
        self.verse_table_model.setHorizontalHeaderLabels(headers)

        # Add data
        for entry in verse_analysis:
            row = []

            # Verse number
            verse_item = QStandardItem(str(entry.verse_number))
            verse_item.setData(entry.verse_number, Qt.ItemDataRole.UserRole)
            row.append(verse_item)

            # Chapter number
            chapter_item = QStandardItem(str(entry.chapter_number) if entry.chapter_number else "N/A")
            row.append(chapter_item)

            # Text (truncated for display)
            text = entry.verse_text[:80] + "..." if len(entry.verse_text) > 80 else entry.verse_text
            text_item = QStandardItem(text)
            text_item.setToolTip(entry.verse_text)  # Full text in tooltip
            row.append(text_item)

            # Word count
            word_count_item = QStandardItem(str(entry.word_count))
            word_count_item.setData(entry.word_count, Qt.ItemDataRole.UserRole)
            row.append(word_count_item)

            # Number count
            number_count_item = QStandardItem(str(entry.number_count))
            number_count_item.setData(entry.number_count, Qt.ItemDataRole.UserRole)
            row.append(number_count_item)

            # Gematria total (show first calculation type)
            if entry.gematria_sums:
                first_gematria = list(entry.gematria_sums.values())[0]
                gematria_item = QStandardItem(str(first_gematria))
                gematria_item.setData(first_gematria, Qt.ItemDataRole.UserRole)
                # Show all values in tooltip
                tooltip_text = "\n".join([f"{k}: {v}" for k, v in entry.gematria_sums.items()])
                gematria_item.setToolTip(f"Gematria values:\n{tooltip_text}")
            else:
                gematria_item = QStandardItem("0")
                gematria_item.setData(0, Qt.ItemDataRole.UserRole)
            row.append(gematria_item)

            # Verse total (gematria + numbers, show first calculation type)
            if entry.verse_totals:
                first_total = list(entry.verse_totals.values())[0]
                total_item = QStandardItem(str(first_total))
                total_item.setData(first_total, Qt.ItemDataRole.UserRole)
                # Show all values in tooltip
                tooltip_text = "\n".join([f"{k}: {v}" for k, v in entry.verse_totals.items()])
                total_item.setToolTip(f"Verse totals:\n{tooltip_text}")
            else:
                total_item = QStandardItem(str(entry.numbers_total))
                total_item.setData(entry.numbers_total, Qt.ItemDataRole.UserRole)
            row.append(total_item)

            self.verse_table_model.appendRow(row)

        # Resize columns
        header = self.verse_table_view.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # Verse
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)  # Chapter
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.Stretch)  # Text
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)  # Words
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)  # Numbers
        header.setSectionResizeMode(5, QHeaderView.ResizeMode.ResizeToContents)  # Gematria Total
        header.setSectionResizeMode(6, QHeaderView.ResizeMode.ResizeToContents)  # Verse Total

    def _populate_calc_type_filter(self, entries: List[GematriaDictionaryEntry]):
        """Populate the calculation type filter with available types."""
        current_text = self.calc_type_filter.currentText()
        self.calc_type_filter.clear()
        self.calc_type_filter.addItem("All Types")

        # Get unique calculation types
        calc_types = set()
        for entry in entries:
            for calc_type in entry.gematria_values.keys():
                calc_types.add(calc_type)

        # Add sorted types
        for calc_type in sorted(calc_types):
            self.calc_type_filter.addItem(calc_type)

        # Restore selection if possible
        index = self.calc_type_filter.findText(current_text)
        if index >= 0:
            self.calc_type_filter.setCurrentIndex(index)

    def _format_global_analysis(self, global_analysis):
        """Format the global analysis summary for display."""
        if hasattr(global_analysis, 'total_verses'):
            # It's a GlobalAnalysisSummary object
            lines = []
            lines.append(f"Total Verses: {global_analysis.total_verses}")
            lines.append(f"Triangular Number: {global_analysis.triangular_number}")
            lines.append("")
            lines.append("Total Gematria Sums by Calculation Type:")
            for calc_type, value in global_analysis.total_gematria_sums.items():
                lines.append(f"  {calc_type}: {value}")
            lines.append("")
            lines.append("Global Sums (Gematria + Triangular):")
            for calc_type, value in global_analysis.global_sums.items():
                lines.append(f"  {calc_type}: {value}")
            return "\n".join(lines)
        else:
            # It's already a string
            return str(global_analysis)

    def _apply_word_filters(self):
        """Apply filters to the word dictionary table."""
        if not self.word_proxy_model:
            return

        # Word filter
        word_filter = self.word_filter.text().strip()
        if word_filter:
            self.word_proxy_model.setFilterKeyColumn(0)  # Word column
            self.word_proxy_model.setFilterFixedString(word_filter)
        else:
            self.word_proxy_model.setFilterFixedString("")

        # Additional filters would be implemented here
        # For now, just the word filter is active

    def _clear_word_filters(self):
        """Clear all word dictionary filters."""
        self.word_filter.clear()
        self.language_filter.setCurrentIndex(0)
        self.value_filter.setValue(0)
        self.calc_type_filter.setCurrentIndex(0)
        self._apply_word_filters()

    def _export_results(self):
        """Export analysis results to file."""
        if not self.current_analysis_result:
            MessageBox.warning(self, "Warning", "No analysis results to export.")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "Export Analysis Results",
            f"{self.analysis_title_input.text() or 'analysis'}_results.txt",
            "Text Files (*.txt);;CSV Files (*.csv);;All Files (*)"
        )

        if file_path:
            try:
                if file_path.endswith('.csv'):
                    self._export_to_csv(file_path)
                else:
                    self._export_to_text(file_path)

                MessageBox.information(self, "Success", f"Results exported to {Path(file_path).name}")

            except Exception as e:
                logger.error(f"Error exporting results: {e}")
                MessageBox.error(self, "Error", f"Failed to export results: {str(e)}")

    def _export_to_text(self, file_path: str):
        """Export results to text format."""
        with open(file_path, 'w', encoding='utf-8') as file:
            result = self.current_analysis_result

            file.write(f"Gematria Analysis Results\n")
            file.write(f"========================\n\n")
            file.write(f"Document: {result.document_title}\n")
            file.write(f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            # Word Dictionary
            file.write(f"Word Dictionary ({len(result.entries)} entries)\n")
            file.write("-" * 50 + "\n")
            for entry in result.entries:
                # Show first gematria value for simplicity
                first_value = list(entry.gematria_values.values())[0] if entry.gematria_values else 0
                file.write(f"{entry.word:<20} "
                          f"{first_value:<8} "
                          f"{entry.frequency:<6} "
                          f"{entry.language.value:<10}\n")

            # Verse Analysis
            if result.verse_analysis:
                file.write(f"\n\nVerse Analysis ({len(result.verse_analysis)} verses)\n")
                file.write("-" * 50 + "\n")
                for entry in result.verse_analysis:
                    file.write(f"Verse {entry.verse_number}: {entry.total_value} "
                              f"(Triangular: {entry.triangular_number})\n")
                    file.write(f"  {entry.verse_text}\n\n")

            # Global Analysis
            if result.global_analysis:
                file.write(f"\n\nGlobal Analysis\n")
                file.write("-" * 50 + "\n")
                file.write(self._format_global_analysis(result.global_analysis))

    def _export_to_csv(self, file_path: str):
        """Export results to CSV format."""
        import csv

        with open(file_path, 'w', newline='', encoding='utf-8') as file:
            writer = csv.writer(file)

            # Write word dictionary
            writer.writerow(["Word", "Language", "Frequency", "Verses", "Gematria Values"])
            for entry in self.current_analysis_result.entries:
                verses_text = ", ".join(map(str, sorted(entry.verse_numbers))) if entry.verse_numbers else ""
                values_text = "; ".join([f"{k}: {v}" for k, v in entry.gematria_values.items()])
                writer.writerow([
                    entry.word,
                    entry.language.value,
                    entry.frequency,
                    verses_text,
                    values_text
                ])

    def _on_word_double_clicked(self, index):
        """Handle double-click on word in dictionary table."""
        if not index.isValid():
            return

        # Get the word from the first column
        word_index = self.word_proxy_model.index(index.row(), 0)
        word = self.word_proxy_model.data(word_index, Qt.ItemDataRole.DisplayRole)

        if word:
            # Get gematria values for this word
            method = self.method_combo.currentData()
            try:
                value = self.gematria_service.calculate(word, method)
                values_dict = {method.name: value}

                # Emit signal for other components
                self.word_selected.emit(word, values_dict)

                # Show detailed information
                MessageBox.information(
                    self, "Word Details",
                    f"Word: {word}\n"
                    f"Gematria Value: {value}\n"
                    f"Method: {method.name}"
                )

            except Exception as e:
                logger.error(f"Error calculating word value: {e}")
                MessageBox.error(self, "Error", f"Failed to calculate word value: {str(e)}")

    def _on_verse_double_clicked(self, index):
        """Handle double-click on verse in analysis table."""
        if not index.isValid():
            return

        # Get verse number from first column
        verse_index = self.verse_table_model.index(index.row(), 0)
        verse_number = self.verse_table_model.data(verse_index, Qt.ItemDataRole.UserRole)

        if verse_number and self.current_analysis_result and self.current_analysis_result.verse_analysis:
            # Find the verse entry
            verse_entry = None
            for entry in self.current_analysis_result.verse_analysis:
                if entry.verse_number == verse_number:
                    verse_entry = entry
                    break

            if verse_entry:
                # Get detailed verse breakdown from service
                try:
                    verse_breakdown = self.dictionary_service.get_verse_breakdown(
                        self.current_analysis_text, verse_number
                    )
                    if verse_breakdown:
                        # Show detailed verse breakdown using UnifiedAnalysisDialog
                        dialog = UnifiedAnalysisDialog(self)

                        # Switch to verse breakdown tab and populate with verse text
                        dialog.tab_widget.setCurrentIndex(1)  # Verse Breakdown tab
                        dialog.verse_number_spin.setValue(verse_number)
                        dialog.verse_text_input.setPlainText(verse_breakdown.verse_text)

                        # Show the dialog
                        dialog.exec()
                    else:
                        MessageBox.warning(self, "Warning", f"Could not find verse {verse_number} in the text.")
                except Exception as e:
                    logger.error(f"Error getting verse breakdown: {e}")
                    MessageBox.error(self, "Error", f"Failed to get verse breakdown: {str(e)}")

    # Text Synchronization Methods

    def _on_explorer_text_changed(self):
        """Handle text changes in the Interactive Explorer tab."""
        if self._syncing_text:
            return

        self._syncing_text = True
        try:
            content = self.doc_content_display.toPlainText()
            self.analysis_text_input.setPlainText(content)

            # Update analysis title if we have a current document
            if self.current_document:
                self.analysis_title_input.setText(self.current_document.name)
        finally:
            self._syncing_text = False

    def _on_analysis_text_changed(self):
        """Handle text changes in the Deep Analysis tab."""
        if self._syncing_text:
            return

        self._syncing_text = True
        try:
            content = self.analysis_text_input.toPlainText()
            self.doc_content_display.setPlainText(content)

            # Clear analysis results when text changes manually
            if hasattr(self, 'current_analysis_result') and self.current_analysis_result:
                # Only clear if the text actually changed from the analyzed text
                if content != self.current_analysis_text:
                    self._clear_analysis_results()
        finally:
            self._syncing_text = False

    # Public API Methods

    def load_document_from_interactive(self):
        """Load current document content into analysis tab."""
        if not self.current_document:
            MessageBox.warning(self, "Warning", "No document loaded in Interactive Explorer.")
            return

        content = self.doc_content_display.toPlainText()
        if not content:
            MessageBox.warning(self, "Warning", "No content available to analyze.")
            return

        # Switch to analysis tab
        self.tab_widget.setCurrentIndex(1)

        # Load content
        self.analysis_text_input.setPlainText(content)
        self.analysis_title_input.setText(self.current_document.name)

        MessageBox.information(
            self, "Content Loaded",
            f"Document content loaded into analysis tab.\n\n"
            f"You can now perform comprehensive gematria analysis."
        )

    def get_current_document(self) -> Optional[Document]:
        """Get the currently loaded document."""
        return self.current_document

    def get_current_analysis_result(self) -> Optional[GematriaDictionaryResult]:
        """Get the current analysis result."""
        return self.current_analysis_result

    # Verse Saving Methods

    def _on_verse_selection_changed(self):
        """Handle verse table selection changes."""
        selection = self.verse_table_view.selectionModel()
        has_selection = selection.hasSelection()

        # Check if we have verse analysis data
        has_verse_analysis = False
        try:
            if (self.current_analysis_result and
                hasattr(self.current_analysis_result, 'verse_analysis') and
                self.current_analysis_result.verse_analysis is not None and
                len(self.current_analysis_result.verse_analysis) > 0):
                has_verse_analysis = True
        except Exception as e:
            logger.error(f"Error checking verse analysis: {e}")
            has_verse_analysis = False

        # Ensure both values are boolean
        has_selection = bool(has_selection)
        has_verse_analysis = bool(has_verse_analysis)

        self.save_selected_verse_btn.setEnabled(has_selection and has_verse_analysis)

    def _save_selected_verse(self):
        """Save the selected verse to the database."""
        selection = self.verse_table_view.selectionModel()
        if not selection.hasSelection():
            MessageBox.warning(self, "Warning", "Please select a verse to save.")
            return

        selected_indexes = selection.selectedRows()
        if not selected_indexes:
            MessageBox.warning(self, "Warning", "Please select a verse to save.")
            return

        # Get the verse number from the first selected row
        verse_index = selected_indexes[0]
        verse_number = self.verse_table_model.data(
            self.verse_table_model.index(verse_index.row(), 0),
            Qt.ItemDataRole.UserRole
        )

        if verse_number and self.current_analysis_result and self.current_analysis_result.verse_analysis:
            # Find the verse entry
            verse_entry = None
            for entry in self.current_analysis_result.verse_analysis:
                if entry.verse_number == verse_number:
                    verse_entry = entry
                    break

            if verse_entry:
                self._save_verse_to_database(verse_entry)
            else:
                MessageBox.error(self, "Error", f"Could not find verse {verse_number} data.")

    def _save_all_verses(self):
        """Save all verses to the database."""
        if not self.current_analysis_result or not self.current_analysis_result.verse_analysis:
            MessageBox.warning(self, "Warning", "No verse analysis available to save.")
            return

        # Ask for confirmation
        verse_count = len(self.current_analysis_result.verse_analysis)
        reply = MessageBox.question(
            self, "Confirm Save All",
            f"Are you sure you want to save all {verse_count} verses to the database?\n\n"
            f"This will create {verse_count} new entries in your gematria database.",
            MessageBox.StandardButton.Yes | MessageBox.StandardButton.No,
            MessageBox.StandardButton.No
        )

        if reply == MessageBox.StandardButton.Yes:
            saved_count = 0
            failed_count = 0

            for verse_entry in self.current_analysis_result.verse_analysis:
                try:
                    self._save_verse_to_database(verse_entry, show_confirmation=False)
                    saved_count += 1
                except Exception as e:
                    logger.error(f"Error saving verse {verse_entry.verse_number}: {e}")
                    failed_count += 1

            # Show summary
            if failed_count == 0:
                MessageBox.information(
                    self, "Save Complete",
                    f"Successfully saved all {saved_count} verses to the database."
                )
            else:
                MessageBox.warning(
                    self, "Save Completed with Errors",
                    f"Saved {saved_count} verses successfully.\n"
                    f"{failed_count} verses failed to save. Check the logs for details."
                )

    def _save_verse_to_database(self, verse_entry: VerseAnalysisEntry, show_confirmation: bool = True):
        """Save a single verse to the database.

        Args:
            verse_entry: The verse analysis entry to save
            show_confirmation: Whether to show confirmation dialog
        """
        try:
            # Get current calculation method
            method = self.method_combo.currentData()

            # Get the document title for the book name (remove .txt extension)
            book_name = self.current_analysis_result.document_title if self.current_analysis_result else "Unknown Book"
            if book_name.endswith('.txt'):
                book_name = book_name[:-4]

            # Format the text as "book": "verse#"
            formatted_text = f'"{book_name}": "{verse_entry.verse_number}"'

            # Get the verse total for the current calculation method
            # Use display_name since that's how the values are stored in the dictionaries
            method_key = getattr(method, 'display_name', str(method))

            if verse_entry.verse_totals and method_key in verse_entry.verse_totals:
                verse_value = verse_entry.verse_totals[method_key]
            elif verse_entry.gematria_sums and method_key in verse_entry.gematria_sums:
                # Fallback to gematria sum if verse total not available
                verse_value = verse_entry.gematria_sums[method_key] + verse_entry.numbers_total
            else:
                # Last fallback to numbers total
                verse_value = verse_entry.numbers_total

            # Create enhanced notes with verse details
            notes_parts = [
                f"Verse text: {verse_entry.verse_text}",
                f"Word count: {verse_entry.word_count}",
                f"Number count: {verse_entry.number_count}",
                f"Numbers total: {verse_entry.numbers_total}"
            ]

            if verse_entry.gematria_sums:
                notes_parts.append("Gematria values:")
                for calc_type, value in verse_entry.gematria_sums.items():
                    notes_parts.append(f"  {calc_type}: {value}")

            if verse_entry.verse_totals:
                notes_parts.append("Verse totals (gematria + numbers):")
                for calc_type, value in verse_entry.verse_totals.items():
                    notes_parts.append(f"  {calc_type}: {value}")

            enhanced_notes = "\n".join(notes_parts)

            # Use the enhanced saving dialog if available
            if SAVE_DIALOG_AVAILABLE:
                method_name = getattr(method, 'display_name', str(method))
                save_dialog = SaveCalculationDialog(verse_value, formatted_text, method_name, self)

                # Pre-populate notes
                save_dialog.notes_edit.setPlainText(enhanced_notes)

                if save_dialog.exec() == QDialog.DialogCode.Accepted:
                    # Get the data from the dialog
                    tags = save_dialog.selected_tags
                    final_notes = save_dialog.notes
                    favorite = save_dialog.is_favorite

                    # Save to database
                    self.gematria_service.calculate_and_save(
                        text=formatted_text,
                        calculation_type=method,
                        notes=final_notes,
                        tags=tags,
                        favorite=favorite,
                        value=verse_value
                    )

                    if show_confirmation:
                        MessageBox.information(
                            self, "Verse Saved",
                            f"Verse {verse_entry.verse_number} saved to database.\n"
                            f"Text: {formatted_text}\n"
                            f"Value: {verse_value}"
                        )
                else:
                    # User cancelled
                    return
            else:
                # Fallback to simple save
                self.gematria_service.calculate_and_save(
                    text=formatted_text,
                    calculation_type=method,
                    notes=enhanced_notes,
                    tags=[],
                    favorite=False,
                    value=verse_value
                )

                if show_confirmation:
                    MessageBox.information(
                        self, "Verse Saved",
                        f"Verse {verse_entry.verse_number} saved to database.\n"
                        f"Text: {formatted_text}\n"
                        f"Value: {verse_value}"
                    )

            logger.debug(f"Saved verse {verse_entry.verse_number} to database: {formatted_text} = {verse_value}")

        except Exception as e:
            logger.error(f"Error saving verse to database: {e}")
            if show_confirmation:
                MessageBox.error(self, "Error", f"Failed to save verse to database: {str(e)}")
            raise






