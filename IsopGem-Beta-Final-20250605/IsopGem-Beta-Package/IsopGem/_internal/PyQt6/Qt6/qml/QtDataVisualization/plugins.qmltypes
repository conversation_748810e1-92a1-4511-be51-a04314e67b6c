import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    Component {
        file: "private/abstractdeclarative_p.h"
        name: "AbstractDeclarative"
        accessSemantics: "reference"
        defaultProperty: "data"
        parentProperty: "parent"
        prototype: "QQuickItem"
        exports: [
            "QtDataVisualization/AbstractGraph3D 1.0",
            "QtDataVisualization/AbstractGraph3D 1.1",
            "QtDataVisualization/AbstractGraph3D 1.2",
            "QtDataVisualization/AbstractGraph3D 2.0",
            "QtDataVisualization/AbstractGraph3D 2.1",
            "QtDataVisualization/AbstractGraph3D 2.4",
            "QtDataVisualization/AbstractGraph3D 2.7",
            "QtDataVisualization/AbstractGraph3D 2.11",
            "QtDataVisualization/AbstractGraph3D 6.0",
            "QtDataVisualization/AbstractGraph3D 6.3",
            "QtDataVisualization/AbstractGraph3D 6.7"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [
            256,
            257,
            258,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Enum {
            name: "SelectionFlag"
            isFlag: true
            values: [
                "SelectionNone",
                "SelectionItem",
                "SelectionRow",
                "SelectionItemAndRow",
                "SelectionColumn",
                "SelectionItemAndColumn",
                "SelectionRowAndColumn",
                "SelectionItemRowAndColumn",
                "SelectionSlice",
                "SelectionMultiSeries"
            ]
        }
        Enum {
            name: "SelectionFlags"
            alias: "SelectionFlag"
            isFlag: true
            values: [
                "SelectionNone",
                "SelectionItem",
                "SelectionRow",
                "SelectionItemAndRow",
                "SelectionColumn",
                "SelectionItemAndColumn",
                "SelectionRowAndColumn",
                "SelectionItemRowAndColumn",
                "SelectionSlice",
                "SelectionMultiSeries"
            ]
        }
        Enum {
            name: "ShadowQuality"
            values: [
                "ShadowQualityNone",
                "ShadowQualityLow",
                "ShadowQualityMedium",
                "ShadowQualityHigh",
                "ShadowQualitySoftLow",
                "ShadowQualitySoftMedium",
                "ShadowQualitySoftHigh"
            ]
        }
        Enum {
            name: "ElementType"
            values: [
                "ElementNone",
                "ElementSeries",
                "ElementAxisXLabel",
                "ElementAxisYLabel",
                "ElementAxisZLabel",
                "ElementCustomItem"
            ]
        }
        Enum {
            name: "RenderingMode"
            values: [
                "RenderDirectToBackground",
                "RenderDirectToBackground_NoClear",
                "RenderIndirect"
            ]
        }
        Enum {
            name: "OptimizationHint"
            isFlag: true
            values: ["OptimizationDefault", "OptimizationStatic"]
        }
        Enum {
            name: "OptimizationHints"
            alias: "OptimizationHint"
            isFlag: true
            values: ["OptimizationDefault", "OptimizationStatic"]
        }
        Property {
            name: "selectionMode"
            type: "SelectionFlags"
            read: "selectionMode"
            write: "setSelectionMode"
            notify: "selectionModeChanged"
            index: 0
        }
        Property {
            name: "shadowQuality"
            type: "ShadowQuality"
            read: "shadowQuality"
            write: "setShadowQuality"
            notify: "shadowQualityChanged"
            index: 1
        }
        Property {
            name: "shadowsSupported"
            type: "bool"
            read: "shadowsSupported"
            notify: "shadowsSupportedChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "msaaSamples"
            type: "int"
            read: "msaaSamples"
            write: "setMsaaSamples"
            notify: "msaaSamplesChanged"
            index: 3
        }
        Property {
            name: "scene"
            type: "Declarative3DScene"
            isPointer: true
            read: "scene"
            notify: "sceneChanged"
            index: 4
            isReadonly: true
        }
        Property {
            name: "inputHandler"
            type: "QAbstract3DInputHandler"
            isPointer: true
            read: "inputHandler"
            write: "setInputHandler"
            notify: "inputHandlerChanged"
            index: 5
        }
        Property {
            name: "theme"
            type: "Q3DTheme"
            isPointer: true
            read: "theme"
            write: "setTheme"
            notify: "themeChanged"
            index: 6
        }
        Property {
            name: "renderingMode"
            type: "RenderingMode"
            read: "renderingMode"
            write: "setRenderingMode"
            notify: "renderingModeChanged"
            index: 7
        }
        Property {
            name: "measureFps"
            revision: 257
            type: "bool"
            read: "measureFps"
            write: "setMeasureFps"
            notify: "measureFpsChanged"
            index: 8
        }
        Property {
            name: "currentFps"
            revision: 257
            type: "double"
            read: "currentFps"
            notify: "currentFpsChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "customItemList"
            revision: 257
            type: "QCustom3DItem"
            isList: true
            read: "customItemList"
            index: 10
            isReadonly: true
        }
        Property {
            name: "orthoProjection"
            revision: 257
            type: "bool"
            read: "isOrthoProjection"
            write: "setOrthoProjection"
            notify: "orthoProjectionChanged"
            index: 11
        }
        Property {
            name: "selectedElement"
            revision: 257
            type: "ElementType"
            read: "selectedElement"
            notify: "selectedElementChanged"
            index: 12
            isReadonly: true
        }
        Property {
            name: "aspectRatio"
            revision: 257
            type: "double"
            read: "aspectRatio"
            write: "setAspectRatio"
            notify: "aspectRatioChanged"
            index: 13
        }
        Property {
            name: "optimizationHints"
            revision: 257
            type: "OptimizationHints"
            read: "optimizationHints"
            write: "setOptimizationHints"
            notify: "optimizationHintsChanged"
            index: 14
        }
        Property {
            name: "polar"
            revision: 258
            type: "bool"
            read: "isPolar"
            write: "setPolar"
            notify: "polarChanged"
            index: 15
        }
        Property {
            name: "radialLabelOffset"
            revision: 258
            type: "float"
            read: "radialLabelOffset"
            write: "setRadialLabelOffset"
            notify: "radialLabelOffsetChanged"
            index: 16
        }
        Property {
            name: "horizontalAspectRatio"
            revision: 258
            type: "double"
            read: "horizontalAspectRatio"
            write: "setHorizontalAspectRatio"
            notify: "horizontalAspectRatioChanged"
            index: 17
        }
        Property {
            name: "reflection"
            revision: 258
            type: "bool"
            read: "isReflection"
            write: "setReflection"
            notify: "reflectionChanged"
            index: 18
        }
        Property {
            name: "reflectivity"
            revision: 258
            type: "double"
            read: "reflectivity"
            write: "setReflectivity"
            notify: "reflectivityChanged"
            index: 19
        }
        Property {
            name: "locale"
            revision: 258
            type: "QLocale"
            read: "locale"
            write: "setLocale"
            notify: "localeChanged"
            index: 20
        }
        Property {
            name: "queriedGraphPosition"
            revision: 258
            type: "QVector3D"
            read: "queriedGraphPosition"
            notify: "queriedGraphPositionChanged"
            index: 21
            isReadonly: true
        }
        Property {
            name: "margin"
            revision: 258
            type: "double"
            read: "margin"
            write: "setMargin"
            notify: "marginChanged"
            index: 22
        }
        Signal {
            name: "selectionModeChanged"
            Parameter { name: "mode"; type: "AbstractDeclarative::SelectionFlags" }
        }
        Signal {
            name: "shadowQualityChanged"
            Parameter { name: "quality"; type: "AbstractDeclarative::ShadowQuality" }
        }
        Signal {
            name: "shadowsSupportedChanged"
            Parameter { name: "supported"; type: "bool" }
        }
        Signal {
            name: "msaaSamplesChanged"
            Parameter { name: "samples"; type: "int" }
        }
        Signal {
            name: "sceneChanged"
            Parameter { name: "scene"; type: "Q3DScene"; isPointer: true }
        }
        Signal {
            name: "inputHandlerChanged"
            Parameter { name: "inputHandler"; type: "QAbstract3DInputHandler"; isPointer: true }
        }
        Signal {
            name: "themeChanged"
            Parameter { name: "theme"; type: "Q3DTheme"; isPointer: true }
        }
        Signal {
            name: "renderingModeChanged"
            Parameter { name: "mode"; type: "AbstractDeclarative::RenderingMode" }
        }
        Signal {
            name: "measureFpsChanged"
            revision: 257
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "currentFpsChanged"
            revision: 257
            Parameter { name: "fps"; type: "double" }
        }
        Signal {
            name: "selectedElementChanged"
            revision: 257
            Parameter { name: "type"; type: "AbstractDeclarative::ElementType" }
        }
        Signal {
            name: "orthoProjectionChanged"
            revision: 257
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "aspectRatioChanged"
            revision: 257
            Parameter { name: "ratio"; type: "double" }
        }
        Signal {
            name: "optimizationHintsChanged"
            revision: 257
            Parameter { name: "hints"; type: "AbstractDeclarative::OptimizationHints" }
        }
        Signal {
            name: "polarChanged"
            revision: 258
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "radialLabelOffsetChanged"
            revision: 258
            Parameter { name: "offset"; type: "float" }
        }
        Signal {
            name: "horizontalAspectRatioChanged"
            revision: 258
            Parameter { name: "ratio"; type: "double" }
        }
        Signal {
            name: "reflectionChanged"
            revision: 258
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "reflectivityChanged"
            revision: 258
            Parameter { name: "reflectivity"; type: "double" }
        }
        Signal {
            name: "localeChanged"
            revision: 258
            Parameter { name: "locale"; type: "QLocale" }
        }
        Signal {
            name: "queriedGraphPositionChanged"
            revision: 258
            Parameter { name: "data"; type: "QVector3D" }
        }
        Signal {
            name: "marginChanged"
            revision: 258
            Parameter { name: "margin"; type: "double" }
        }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "windowDestroyed"
            Parameter { name: "obj"; type: "QObject"; isPointer: true }
        }
        Method { name: "destroyContext" }
        Method { name: "clearSelection" }
        Method {
            name: "hasSeries"
            revision: 1539
            type: "bool"
            Parameter { name: "series"; type: "QAbstract3DSeries"; isPointer: true }
        }
        Method {
            name: "addCustomItem"
            revision: 257
            type: "int"
            Parameter { name: "item"; type: "QCustom3DItem"; isPointer: true }
        }
        Method { name: "removeCustomItems"; revision: 257 }
        Method {
            name: "removeCustomItem"
            revision: 257
            Parameter { name: "item"; type: "QCustom3DItem"; isPointer: true }
        }
        Method {
            name: "removeCustomItemAt"
            revision: 257
            Parameter { name: "position"; type: "QVector3D" }
        }
        Method {
            name: "releaseCustomItem"
            revision: 257
            Parameter { name: "item"; type: "QCustom3DItem"; isPointer: true }
        }
        Method { name: "selectedLabelIndex"; revision: 257; type: "int"; isMethodConstant: true }
        Method {
            name: "selectedAxis"
            revision: 257
            type: "QAbstract3DAxis"
            isPointer: true
            isMethodConstant: true
        }
        Method { name: "selectedCustomItemIndex"; revision: 257; type: "int"; isMethodConstant: true }
        Method {
            name: "selectedCustomItem"
            revision: 257
            type: "QCustom3DItem"
            isPointer: true
            isMethodConstant: true
        }
    }
    Component {
        file: "private/colorgradient_p.h"
        name: "ColorGradient"
        accessSemantics: "reference"
        defaultProperty: "stops"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/ColorGradient 1.0",
            "QtDataVisualization/ColorGradient 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "stops"
            type: "ColorGradientStop"
            isList: true
            read: "stops"
            index: 0
            isReadonly: true
        }
        Signal { name: "updated" }
    }
    Component {
        file: "private/colorgradient_p.h"
        name: "ColorGradientStop"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/ColorGradientStop 1.0",
            "QtDataVisualization/ColorGradientStop 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "position"
            type: "double"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 0
        }
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 1
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "double" }
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        file: "private/declarativescene_p.h"
        name: "Declarative3DScene"
        accessSemantics: "reference"
        prototype: "Q3DScene"
        exports: [
            "QtDataVisualization/Scene3D 1.0",
            "QtDataVisualization/Scene3D 1.2",
            "QtDataVisualization/Scene3D 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 258, 1536]
        Property {
            name: "selectionQueryPosition"
            type: "QPointF"
            read: "selectionQueryPosition"
            write: "setSelectionQueryPosition"
            notify: "selectionQueryPositionChanged"
            index: 0
        }
        Property {
            name: "invalidSelectionPoint"
            type: "QPoint"
            read: "invalidSelectionPoint"
            index: 1
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "selectionQueryPositionChanged"
            Parameter { name: "position"; type: "QPointF" }
        }
    }
    Component {
        file: "private/declarativeseries_p.h"
        name: "DeclarativeBar3DSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QBar3DSeries"
        exports: [
            "QtDataVisualization/Bar3DSeries 1.0",
            "QtDataVisualization/Bar3DSeries 1.1",
            "QtDataVisualization/Bar3DSeries 6.0",
            "QtDataVisualization/Bar3DSeries 6.3"
        ]
        exportMetaObjectRevisions: [256, 257, 1536, 1539]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
        }
        Property {
            name: "selectedBar"
            type: "QPointF"
            read: "selectedBar"
            write: "setSelectedBar"
            notify: "selectedBarChanged"
            index: 1
        }
        Property {
            name: "invalidSelectionPosition"
            type: "QPointF"
            read: "invalidSelectionPosition"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "baseGradient"
            type: "ColorGradient"
            isPointer: true
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 3
        }
        Property {
            name: "singleHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 4
        }
        Property {
            name: "multiHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 5
        }
        Property {
            name: "rowColors"
            revision: 1539
            type: "DeclarativeColor"
            isList: true
            read: "rowColors"
            index: 6
            isReadonly: true
        }
        Signal {
            name: "selectedBarChanged"
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Method { name: "handleBaseGradientUpdate" }
        Method { name: "handleSingleHighlightGradientUpdate" }
        Method { name: "handleMultiHighlightGradientUpdate" }
        Method { name: "handleRowColorUpdate" }
    }
    Component {
        file: "private/declarativebars_p.h"
        name: "DeclarativeBars"
        accessSemantics: "reference"
        defaultProperty: "seriesList"
        prototype: "AbstractDeclarative"
        exports: [
            "QtDataVisualization/Bars3D 1.0",
            "QtDataVisualization/Bars3D 1.1",
            "QtDataVisualization/Bars3D 1.2",
            "QtDataVisualization/Bars3D 2.0",
            "QtDataVisualization/Bars3D 2.1",
            "QtDataVisualization/Bars3D 2.4",
            "QtDataVisualization/Bars3D 2.7",
            "QtDataVisualization/Bars3D 2.11",
            "QtDataVisualization/Bars3D 6.0",
            "QtDataVisualization/Bars3D 6.3",
            "QtDataVisualization/Bars3D 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            258,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "rowAxis"
            type: "QCategory3DAxis"
            isPointer: true
            read: "rowAxis"
            write: "setRowAxis"
            notify: "rowAxisChanged"
            index: 0
        }
        Property {
            name: "valueAxis"
            type: "QValue3DAxis"
            isPointer: true
            read: "valueAxis"
            write: "setValueAxis"
            notify: "valueAxisChanged"
            index: 1
        }
        Property {
            name: "columnAxis"
            type: "QCategory3DAxis"
            isPointer: true
            read: "columnAxis"
            write: "setColumnAxis"
            notify: "columnAxisChanged"
            index: 2
        }
        Property {
            name: "multiSeriesUniform"
            type: "bool"
            read: "isMultiSeriesUniform"
            write: "setMultiSeriesUniform"
            notify: "multiSeriesUniformChanged"
            index: 3
        }
        Property {
            name: "barThickness"
            type: "float"
            read: "barThickness"
            write: "setBarThickness"
            notify: "barThicknessChanged"
            index: 4
        }
        Property {
            name: "barSpacing"
            type: "QSizeF"
            read: "barSpacing"
            write: "setBarSpacing"
            notify: "barSpacingChanged"
            index: 5
        }
        Property {
            name: "barSpacingRelative"
            type: "bool"
            read: "isBarSpacingRelative"
            write: "setBarSpacingRelative"
            notify: "barSpacingRelativeChanged"
            index: 6
        }
        Property {
            name: "barSeriesMargin"
            revision: 1539
            type: "QSizeF"
            read: "barSeriesMargin"
            write: "setBarSeriesMargin"
            notify: "barSeriesMarginChanged"
            index: 7
        }
        Property {
            name: "seriesList"
            type: "QBar3DSeries"
            isList: true
            read: "seriesList"
            index: 8
            isReadonly: true
        }
        Property {
            name: "selectedSeries"
            type: "QBar3DSeries"
            isPointer: true
            read: "selectedSeries"
            notify: "selectedSeriesChanged"
            index: 9
            isReadonly: true
        }
        Property {
            name: "primarySeries"
            type: "QBar3DSeries"
            isPointer: true
            read: "primarySeries"
            write: "setPrimarySeries"
            notify: "primarySeriesChanged"
            index: 10
        }
        Property {
            name: "floorLevel"
            revision: 258
            type: "float"
            read: "floorLevel"
            write: "setFloorLevel"
            notify: "floorLevelChanged"
            index: 11
        }
        Signal {
            name: "rowAxisChanged"
            Parameter { name: "axis"; type: "QCategory3DAxis"; isPointer: true }
        }
        Signal {
            name: "valueAxisChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "columnAxisChanged"
            Parameter { name: "axis"; type: "QCategory3DAxis"; isPointer: true }
        }
        Signal {
            name: "multiSeriesUniformChanged"
            Parameter { name: "uniform"; type: "bool" }
        }
        Signal {
            name: "barThicknessChanged"
            Parameter { name: "thicknessRatio"; type: "float" }
        }
        Signal {
            name: "barSpacingChanged"
            Parameter { name: "spacing"; type: "QSizeF" }
        }
        Signal {
            name: "barSpacingRelativeChanged"
            Parameter { name: "relative"; type: "bool" }
        }
        Signal {
            name: "barSeriesMarginChanged"
            revision: 1539
            Parameter { name: "margin"; type: "QSizeF" }
        }
        Signal {
            name: "meshFileNameChanged"
            Parameter { name: "filename"; type: "QString" }
        }
        Signal {
            name: "primarySeriesChanged"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Signal {
            name: "selectedSeriesChanged"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Signal {
            name: "floorLevelChanged"
            revision: 258
            Parameter { name: "level"; type: "float" }
        }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "addSeries"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
        Method {
            name: "insertSeries"
            Parameter { name: "index"; type: "int" }
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "private/declarativecolor_p.h"
        name: "DeclarativeColor"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/ThemeColor 1.0",
            "QtDataVisualization/ThemeColor 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "color"
            type: "QColor"
            read: "color"
            write: "setColor"
            notify: "colorChanged"
            index: 0
        }
        Signal {
            name: "colorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        file: "private/declarativescatter_p.h"
        name: "DeclarativeScatter"
        accessSemantics: "reference"
        defaultProperty: "seriesList"
        prototype: "AbstractDeclarative"
        exports: [
            "QtDataVisualization/Scatter3D 1.0",
            "QtDataVisualization/Scatter3D 1.1",
            "QtDataVisualization/Scatter3D 1.2",
            "QtDataVisualization/Scatter3D 2.0",
            "QtDataVisualization/Scatter3D 2.1",
            "QtDataVisualization/Scatter3D 2.4",
            "QtDataVisualization/Scatter3D 2.7",
            "QtDataVisualization/Scatter3D 2.11",
            "QtDataVisualization/Scatter3D 6.0",
            "QtDataVisualization/Scatter3D 6.3",
            "QtDataVisualization/Scatter3D 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            258,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "axisX"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisZ"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisZ"
            write: "setAxisZ"
            notify: "axisZChanged"
            index: 2
        }
        Property {
            name: "selectedSeries"
            type: "QScatter3DSeries"
            isPointer: true
            read: "selectedSeries"
            notify: "selectedSeriesChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "seriesList"
            type: "QScatter3DSeries"
            isList: true
            read: "seriesList"
            index: 4
            isReadonly: true
        }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisZChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "selectedSeriesChanged"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "addSeries"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "private/declarativeseries_p.h"
        name: "DeclarativeScatter3DSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QScatter3DSeries"
        exports: [
            "QtDataVisualization/Scatter3DSeries 1.0",
            "QtDataVisualization/Scatter3DSeries 1.1",
            "QtDataVisualization/Scatter3DSeries 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
        }
        Property {
            name: "baseGradient"
            type: "ColorGradient"
            isPointer: true
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 1
        }
        Property {
            name: "singleHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 2
        }
        Property {
            name: "multiHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 3
        }
        Property {
            name: "invalidSelectionIndex"
            type: "int"
            read: "invalidSelectionIndex"
            index: 4
            isReadonly: true
            isPropertyConstant: true
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Method { name: "handleBaseGradientUpdate" }
        Method { name: "handleSingleHighlightGradientUpdate" }
        Method { name: "handleMultiHighlightGradientUpdate" }
    }
    Component {
        file: "private/declarativesurface_p.h"
        name: "DeclarativeSurface"
        accessSemantics: "reference"
        defaultProperty: "seriesList"
        prototype: "AbstractDeclarative"
        exports: [
            "QtDataVisualization/Surface3D 1.0",
            "QtDataVisualization/Surface3D 1.1",
            "QtDataVisualization/Surface3D 1.2",
            "QtDataVisualization/Surface3D 2.0",
            "QtDataVisualization/Surface3D 2.1",
            "QtDataVisualization/Surface3D 2.4",
            "QtDataVisualization/Surface3D 2.7",
            "QtDataVisualization/Surface3D 2.11",
            "QtDataVisualization/Surface3D 6.0",
            "QtDataVisualization/Surface3D 6.3",
            "QtDataVisualization/Surface3D 6.7"
        ]
        exportMetaObjectRevisions: [
            256,
            257,
            258,
            512,
            513,
            516,
            519,
            523,
            1536,
            1539,
            1543
        ]
        Property {
            name: "axisX"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisX"
            write: "setAxisX"
            notify: "axisXChanged"
            index: 0
        }
        Property {
            name: "axisY"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisY"
            write: "setAxisY"
            notify: "axisYChanged"
            index: 1
        }
        Property {
            name: "axisZ"
            type: "QValue3DAxis"
            isPointer: true
            read: "axisZ"
            write: "setAxisZ"
            notify: "axisZChanged"
            index: 2
        }
        Property {
            name: "selectedSeries"
            type: "QSurface3DSeries"
            isPointer: true
            read: "selectedSeries"
            notify: "selectedSeriesChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "seriesList"
            type: "QSurface3DSeries"
            isList: true
            read: "seriesList"
            index: 4
            isReadonly: true
        }
        Property {
            name: "flipHorizontalGrid"
            revision: 258
            type: "bool"
            read: "flipHorizontalGrid"
            write: "setFlipHorizontalGrid"
            notify: "flipHorizontalGridChanged"
            index: 5
        }
        Signal {
            name: "axisXChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisYChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "axisZChanged"
            Parameter { name: "axis"; type: "QValue3DAxis"; isPointer: true }
        }
        Signal {
            name: "selectedSeriesChanged"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
        Signal {
            name: "flipHorizontalGridChanged"
            revision: 258
            Parameter { name: "flip"; type: "bool" }
        }
        Method {
            name: "handleAxisXChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisYChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "handleAxisZChanged"
            Parameter { name: "axis"; type: "QAbstract3DAxis"; isPointer: true }
        }
        Method {
            name: "addSeries"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
        Method {
            name: "removeSeries"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "private/declarativeseries_p.h"
        name: "DeclarativeSurface3DSeries"
        accessSemantics: "reference"
        defaultProperty: "seriesChildren"
        prototype: "QSurface3DSeries"
        exports: [
            "QtDataVisualization/Surface3DSeries 1.0",
            "QtDataVisualization/Surface3DSeries 1.1",
            "QtDataVisualization/Surface3DSeries 6.0",
            "QtDataVisualization/Surface3DSeries 6.3"
        ]
        exportMetaObjectRevisions: [256, 257, 1536, 1539]
        Property {
            name: "seriesChildren"
            type: "QObject"
            isList: true
            read: "seriesChildren"
            index: 0
            isReadonly: true
        }
        Property {
            name: "selectedPoint"
            type: "QPointF"
            read: "selectedPoint"
            write: "setSelectedPoint"
            notify: "selectedPointChanged"
            index: 1
        }
        Property {
            name: "invalidSelectionPosition"
            type: "QPointF"
            read: "invalidSelectionPosition"
            index: 2
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "baseGradient"
            type: "ColorGradient"
            isPointer: true
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 3
        }
        Property {
            name: "singleHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 4
        }
        Property {
            name: "multiHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 5
        }
        Signal {
            name: "selectedPointChanged"
            Parameter { name: "position"; type: "QPointF" }
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Method { name: "handleBaseGradientUpdate" }
        Method { name: "handleSingleHighlightGradientUpdate" }
        Method { name: "handleMultiHighlightGradientUpdate" }
    }
    Component {
        file: "private/declarativetheme_p.h"
        name: "DeclarativeTheme3D"
        accessSemantics: "reference"
        defaultProperty: "themeChildren"
        prototype: "Q3DTheme"
        interfaces: ["QQmlParserStatus"]
        exports: [
            "QtDataVisualization/Theme3D 1.0",
            "QtDataVisualization/Theme3D 6.0"
        ]
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "themeChildren"
            type: "QObject"
            isList: true
            read: "themeChildren"
            index: 0
            isReadonly: true
        }
        Property {
            name: "baseColors"
            type: "DeclarativeColor"
            isList: true
            read: "baseColors"
            index: 1
            isReadonly: true
        }
        Property {
            name: "baseGradients"
            type: "ColorGradient"
            isList: true
            read: "baseGradients"
            index: 2
            isReadonly: true
        }
        Property {
            name: "singleHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 3
        }
        Property {
            name: "multiHighlightGradient"
            type: "ColorGradient"
            isPointer: true
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 4
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "ColorGradient"; isPointer: true }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "Q3DCamera"
        accessSemantics: "reference"
        prototype: "Q3DObject"
        exports: [
            "QtDataVisualization/Camera3D 1.0",
            "QtDataVisualization/Camera3D 1.2",
            "QtDataVisualization/Camera3D 6.0"
        ]
        exportMetaObjectRevisions: [256, 258, 1536]
        Enum {
            name: "CameraPreset"
            values: [
                "CameraPresetNone",
                "CameraPresetFrontLow",
                "CameraPresetFront",
                "CameraPresetFrontHigh",
                "CameraPresetLeftLow",
                "CameraPresetLeft",
                "CameraPresetLeftHigh",
                "CameraPresetRightLow",
                "CameraPresetRight",
                "CameraPresetRightHigh",
                "CameraPresetBehindLow",
                "CameraPresetBehind",
                "CameraPresetBehindHigh",
                "CameraPresetIsometricLeft",
                "CameraPresetIsometricLeftHigh",
                "CameraPresetIsometricRight",
                "CameraPresetIsometricRightHigh",
                "CameraPresetDirectlyAbove",
                "CameraPresetDirectlyAboveCW45",
                "CameraPresetDirectlyAboveCCW45",
                "CameraPresetFrontBelow",
                "CameraPresetLeftBelow",
                "CameraPresetRightBelow",
                "CameraPresetBehindBelow",
                "CameraPresetDirectlyBelow"
            ]
        }
        Property {
            name: "xRotation"
            type: "float"
            read: "xRotation"
            write: "setXRotation"
            notify: "xRotationChanged"
            index: 0
        }
        Property {
            name: "yRotation"
            type: "float"
            read: "yRotation"
            write: "setYRotation"
            notify: "yRotationChanged"
            index: 1
        }
        Property {
            name: "zoomLevel"
            type: "float"
            read: "zoomLevel"
            write: "setZoomLevel"
            notify: "zoomLevelChanged"
            index: 2
        }
        Property {
            name: "cameraPreset"
            type: "CameraPreset"
            read: "cameraPreset"
            write: "setCameraPreset"
            notify: "cameraPresetChanged"
            index: 3
        }
        Property {
            name: "wrapXRotation"
            type: "bool"
            read: "wrapXRotation"
            write: "setWrapXRotation"
            notify: "wrapXRotationChanged"
            index: 4
        }
        Property {
            name: "wrapYRotation"
            type: "bool"
            read: "wrapYRotation"
            write: "setWrapYRotation"
            notify: "wrapYRotationChanged"
            index: 5
        }
        Property {
            name: "target"
            revision: 258
            type: "QVector3D"
            read: "target"
            write: "setTarget"
            notify: "targetChanged"
            index: 6
        }
        Property {
            name: "minZoomLevel"
            revision: 258
            type: "float"
            read: "minZoomLevel"
            write: "setMinZoomLevel"
            notify: "minZoomLevelChanged"
            index: 7
        }
        Property {
            name: "maxZoomLevel"
            revision: 258
            type: "float"
            read: "maxZoomLevel"
            write: "setMaxZoomLevel"
            notify: "maxZoomLevelChanged"
            index: 8
        }
        Signal {
            name: "xRotationChanged"
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "yRotationChanged"
            Parameter { name: "rotation"; type: "float" }
        }
        Signal {
            name: "zoomLevelChanged"
            Parameter { name: "zoomLevel"; type: "float" }
        }
        Signal {
            name: "cameraPresetChanged"
            Parameter { name: "preset"; type: "Q3DCamera::CameraPreset" }
        }
        Signal {
            name: "wrapXRotationChanged"
            Parameter { name: "isEnabled"; type: "bool" }
        }
        Signal {
            name: "wrapYRotationChanged"
            Parameter { name: "isEnabled"; type: "bool" }
        }
        Signal {
            name: "targetChanged"
            revision: 258
            Parameter { name: "target"; type: "QVector3D" }
        }
        Signal {
            name: "minZoomLevelChanged"
            revision: 258
            Parameter { name: "zoomLevel"; type: "float" }
        }
        Signal {
            name: "maxZoomLevelChanged"
            revision: 258
            Parameter { name: "zoomLevel"; type: "float" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "Q3DInputHandler"
        accessSemantics: "reference"
        prototype: "QAbstract3DInputHandler"
        exports: [
            "QtDataVisualization/InputHandler3D 1.2",
            "QtDataVisualization/InputHandler3D 6.0"
        ]
        exportMetaObjectRevisions: [258, 1536]
        Property {
            name: "rotationEnabled"
            type: "bool"
            read: "isRotationEnabled"
            write: "setRotationEnabled"
            notify: "rotationEnabledChanged"
            index: 0
        }
        Property {
            name: "zoomEnabled"
            type: "bool"
            read: "isZoomEnabled"
            write: "setZoomEnabled"
            notify: "zoomEnabledChanged"
            index: 1
        }
        Property {
            name: "selectionEnabled"
            type: "bool"
            read: "isSelectionEnabled"
            write: "setSelectionEnabled"
            notify: "selectionEnabledChanged"
            index: 2
        }
        Property {
            name: "zoomAtTargetEnabled"
            type: "bool"
            read: "isZoomAtTargetEnabled"
            write: "setZoomAtTargetEnabled"
            notify: "zoomAtTargetEnabledChanged"
            index: 3
        }
        Signal {
            name: "rotationEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "zoomEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "selectionEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "zoomAtTargetEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "Q3DLight"
        accessSemantics: "reference"
        prototype: "Q3DObject"
        exports: [
            "QtDataVisualization/Light3D 1.0",
            "QtDataVisualization/Light3D 1.3",
            "QtDataVisualization/Light3D 6.0"
        ]
        exportMetaObjectRevisions: [256, 259, 1536]
        Property {
            name: "autoPosition"
            revision: 259
            type: "bool"
            read: "isAutoPosition"
            write: "setAutoPosition"
            notify: "autoPositionChanged"
            index: 0
        }
        Signal {
            name: "autoPositionChanged"
            revision: 259
            Parameter { name: "autoPosition"; type: "bool" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "Q3DObject"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/Object3D 1.0",
            "QtDataVisualization/Object3D 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "parentScene"
            type: "Q3DScene"
            isPointer: true
            read: "parentScene"
            index: 0
            isReadonly: true
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 1
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "Q3DScene"
        accessSemantics: "reference"
        prototype: "QObject"
        Property {
            name: "viewport"
            type: "QRect"
            read: "viewport"
            notify: "viewportChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "primarySubViewport"
            type: "QRect"
            read: "primarySubViewport"
            write: "setPrimarySubViewport"
            notify: "primarySubViewportChanged"
            index: 1
        }
        Property {
            name: "secondarySubViewport"
            type: "QRect"
            read: "secondarySubViewport"
            write: "setSecondarySubViewport"
            notify: "secondarySubViewportChanged"
            index: 2
        }
        Property {
            name: "selectionQueryPosition"
            type: "QPoint"
            read: "selectionQueryPosition"
            write: "setSelectionQueryPosition"
            notify: "selectionQueryPositionChanged"
            index: 3
        }
        Property {
            name: "secondarySubviewOnTop"
            type: "bool"
            read: "isSecondarySubviewOnTop"
            write: "setSecondarySubviewOnTop"
            notify: "secondarySubviewOnTopChanged"
            index: 4
        }
        Property {
            name: "slicingActive"
            type: "bool"
            read: "isSlicingActive"
            write: "setSlicingActive"
            notify: "slicingActiveChanged"
            index: 5
        }
        Property {
            name: "activeCamera"
            type: "Q3DCamera"
            isPointer: true
            read: "activeCamera"
            write: "setActiveCamera"
            notify: "activeCameraChanged"
            index: 6
        }
        Property {
            name: "activeLight"
            type: "Q3DLight"
            isPointer: true
            read: "activeLight"
            write: "setActiveLight"
            notify: "activeLightChanged"
            index: 7
        }
        Property {
            name: "devicePixelRatio"
            type: "float"
            read: "devicePixelRatio"
            write: "setDevicePixelRatio"
            notify: "devicePixelRatioChanged"
            index: 8
        }
        Property {
            name: "graphPositionQuery"
            revision: 258
            type: "QPoint"
            read: "graphPositionQuery"
            write: "setGraphPositionQuery"
            notify: "graphPositionQueryChanged"
            index: 9
        }
        Signal {
            name: "viewportChanged"
            Parameter { name: "viewport"; type: "QRect" }
        }
        Signal {
            name: "primarySubViewportChanged"
            Parameter { name: "subViewport"; type: "QRect" }
        }
        Signal {
            name: "secondarySubViewportChanged"
            Parameter { name: "subViewport"; type: "QRect" }
        }
        Signal {
            name: "secondarySubviewOnTopChanged"
            Parameter { name: "isSecondaryOnTop"; type: "bool" }
        }
        Signal {
            name: "slicingActiveChanged"
            Parameter { name: "isSlicingActive"; type: "bool" }
        }
        Signal {
            name: "activeCameraChanged"
            Parameter { name: "camera"; type: "Q3DCamera"; isPointer: true }
        }
        Signal {
            name: "activeLightChanged"
            Parameter { name: "light"; type: "Q3DLight"; isPointer: true }
        }
        Signal {
            name: "devicePixelRatioChanged"
            Parameter { name: "pixelRatio"; type: "float" }
        }
        Signal {
            name: "selectionQueryPositionChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal {
            name: "graphPositionQueryChanged"
            revision: 258
            Parameter { name: "position"; type: "QPoint" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "Q3DTheme"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/Q3DTheme 1.0",
            "QtDataVisualization/Q3DTheme 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "ColorStyle"
            values: [
                "ColorStyleUniform",
                "ColorStyleObjectGradient",
                "ColorStyleRangeGradient"
            ]
        }
        Enum {
            name: "Theme"
            values: [
                "ThemeQt",
                "ThemePrimaryColors",
                "ThemeDigia",
                "ThemeStoneMoss",
                "ThemeArmyBlue",
                "ThemeRetro",
                "ThemeEbony",
                "ThemeIsabelle",
                "ThemeUserDefined"
            ]
        }
        Property {
            name: "type"
            type: "Theme"
            read: "type"
            write: "setType"
            notify: "typeChanged"
            index: 0
        }
        Property {
            name: "baseColors"
            type: "QColor"
            isList: true
            read: "baseColors"
            write: "setBaseColors"
            notify: "baseColorsChanged"
            index: 1
        }
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            write: "setBackgroundColor"
            notify: "backgroundColorChanged"
            index: 2
        }
        Property {
            name: "windowColor"
            type: "QColor"
            read: "windowColor"
            write: "setWindowColor"
            notify: "windowColorChanged"
            index: 3
        }
        Property {
            name: "labelTextColor"
            type: "QColor"
            read: "labelTextColor"
            write: "setLabelTextColor"
            notify: "labelTextColorChanged"
            index: 4
        }
        Property {
            name: "labelBackgroundColor"
            type: "QColor"
            read: "labelBackgroundColor"
            write: "setLabelBackgroundColor"
            notify: "labelBackgroundColorChanged"
            index: 5
        }
        Property {
            name: "gridLineColor"
            type: "QColor"
            read: "gridLineColor"
            write: "setGridLineColor"
            notify: "gridLineColorChanged"
            index: 6
        }
        Property {
            name: "singleHighlightColor"
            type: "QColor"
            read: "singleHighlightColor"
            write: "setSingleHighlightColor"
            notify: "singleHighlightColorChanged"
            index: 7
        }
        Property {
            name: "multiHighlightColor"
            type: "QColor"
            read: "multiHighlightColor"
            write: "setMultiHighlightColor"
            notify: "multiHighlightColorChanged"
            index: 8
        }
        Property {
            name: "lightColor"
            type: "QColor"
            read: "lightColor"
            write: "setLightColor"
            notify: "lightColorChanged"
            index: 9
        }
        Property {
            name: "baseGradients"
            type: "QLinearGradient"
            isList: true
            read: "baseGradients"
            write: "setBaseGradients"
            notify: "baseGradientsChanged"
            index: 10
        }
        Property {
            name: "singleHighlightGradient"
            type: "QLinearGradient"
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 11
        }
        Property {
            name: "multiHighlightGradient"
            type: "QLinearGradient"
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 12
        }
        Property {
            name: "lightStrength"
            type: "float"
            read: "lightStrength"
            write: "setLightStrength"
            notify: "lightStrengthChanged"
            index: 13
        }
        Property {
            name: "ambientLightStrength"
            type: "float"
            read: "ambientLightStrength"
            write: "setAmbientLightStrength"
            notify: "ambientLightStrengthChanged"
            index: 14
        }
        Property {
            name: "highlightLightStrength"
            type: "float"
            read: "highlightLightStrength"
            write: "setHighlightLightStrength"
            notify: "highlightLightStrengthChanged"
            index: 15
        }
        Property {
            name: "labelBorderEnabled"
            type: "bool"
            read: "isLabelBorderEnabled"
            write: "setLabelBorderEnabled"
            notify: "labelBorderEnabledChanged"
            index: 16
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 17
        }
        Property {
            name: "backgroundEnabled"
            type: "bool"
            read: "isBackgroundEnabled"
            write: "setBackgroundEnabled"
            notify: "backgroundEnabledChanged"
            index: 18
        }
        Property {
            name: "gridEnabled"
            type: "bool"
            read: "isGridEnabled"
            write: "setGridEnabled"
            notify: "gridEnabledChanged"
            index: 19
        }
        Property {
            name: "labelBackgroundEnabled"
            type: "bool"
            read: "isLabelBackgroundEnabled"
            write: "setLabelBackgroundEnabled"
            notify: "labelBackgroundEnabledChanged"
            index: 20
        }
        Property {
            name: "colorStyle"
            type: "ColorStyle"
            read: "colorStyle"
            write: "setColorStyle"
            notify: "colorStyleChanged"
            index: 21
        }
        Signal {
            name: "typeChanged"
            Parameter { name: "themeType"; type: "Q3DTheme::Theme" }
        }
        Signal {
            name: "baseColorsChanged"
            Parameter { name: "colors"; type: "QColor"; isList: true }
        }
        Signal {
            name: "backgroundColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "windowColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "labelTextColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "labelBackgroundColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "gridLineColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "singleHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "multiHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "lightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "baseGradientsChanged"
            Parameter { name: "gradients"; type: "QLinearGradient"; isList: true }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "lightStrengthChanged"
            Parameter { name: "strength"; type: "float" }
        }
        Signal {
            name: "ambientLightStrengthChanged"
            Parameter { name: "strength"; type: "float" }
        }
        Signal {
            name: "highlightLightStrengthChanged"
            Parameter { name: "strength"; type: "float" }
        }
        Signal {
            name: "labelBorderEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "backgroundEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "gridEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "labelBackgroundEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "colorStyleChanged"
            Parameter { name: "style"; type: "Q3DTheme::ColorStyle" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QAbstract3DAxis"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/AbstractAxis3D 1.0",
            "QtDataVisualization/AbstractAxis3D 1.1",
            "QtDataVisualization/AbstractAxis3D 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 257, 1536]
        Enum {
            name: "AxisOrientation"
            values: [
                "AxisOrientationNone",
                "AxisOrientationX",
                "AxisOrientationY",
                "AxisOrientationZ"
            ]
        }
        Enum {
            name: "AxisType"
            values: ["AxisTypeNone", "AxisTypeCategory", "AxisTypeValue"]
        }
        Property {
            name: "title"
            type: "QString"
            read: "title"
            write: "setTitle"
            notify: "titleChanged"
            index: 0
        }
        Property {
            name: "labels"
            type: "QStringList"
            read: "labels"
            write: "setLabels"
            notify: "labelsChanged"
            index: 1
        }
        Property {
            name: "orientation"
            type: "AxisOrientation"
            read: "orientation"
            notify: "orientationChanged"
            index: 2
            isReadonly: true
        }
        Property {
            name: "type"
            type: "AxisType"
            read: "type"
            index: 3
            isReadonly: true
            isPropertyConstant: true
        }
        Property { name: "min"; type: "float"; read: "min"; write: "setMin"; notify: "minChanged"; index: 4 }
        Property { name: "max"; type: "float"; read: "max"; write: "setMax"; notify: "maxChanged"; index: 5 }
        Property {
            name: "autoAdjustRange"
            type: "bool"
            read: "isAutoAdjustRange"
            write: "setAutoAdjustRange"
            notify: "autoAdjustRangeChanged"
            index: 6
        }
        Property {
            name: "labelAutoRotation"
            revision: 257
            type: "float"
            read: "labelAutoRotation"
            write: "setLabelAutoRotation"
            notify: "labelAutoRotationChanged"
            index: 7
        }
        Property {
            name: "titleVisible"
            revision: 257
            type: "bool"
            read: "isTitleVisible"
            write: "setTitleVisible"
            notify: "titleVisibilityChanged"
            index: 8
        }
        Property {
            name: "titleFixed"
            revision: 257
            type: "bool"
            read: "isTitleFixed"
            write: "setTitleFixed"
            notify: "titleFixedChanged"
            index: 9
        }
        Signal {
            name: "titleChanged"
            Parameter { name: "newTitle"; type: "QString" }
        }
        Signal { name: "labelsChanged" }
        Signal {
            name: "orientationChanged"
            Parameter { name: "orientation"; type: "QAbstract3DAxis::AxisOrientation" }
        }
        Signal {
            name: "minChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "rangeChanged"
            Parameter { name: "min"; type: "float" }
            Parameter { name: "max"; type: "float" }
        }
        Signal {
            name: "autoAdjustRangeChanged"
            Parameter { name: "autoAdjust"; type: "bool" }
        }
        Signal {
            name: "labelAutoRotationChanged"
            revision: 257
            Parameter { name: "angle"; type: "float" }
        }
        Signal {
            name: "titleVisibilityChanged"
            revision: 257
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "titleFixedChanged"
            revision: 257
            Parameter { name: "fixed"; type: "bool" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QAbstract3DInputHandler"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/AbstractInputHandler3D 1.0",
            "QtDataVisualization/AbstractInputHandler3D 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "InputView"
            values: [
                "InputViewNone",
                "InputViewOnPrimary",
                "InputViewOnSecondary"
            ]
        }
        Property {
            name: "inputView"
            type: "InputView"
            read: "inputView"
            write: "setInputView"
            notify: "inputViewChanged"
            index: 0
        }
        Property {
            name: "inputPosition"
            type: "QPoint"
            read: "inputPosition"
            write: "setInputPosition"
            notify: "positionChanged"
            index: 1
        }
        Property {
            name: "scene"
            type: "Q3DScene"
            isPointer: true
            read: "scene"
            write: "setScene"
            notify: "sceneChanged"
            index: 2
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal {
            name: "inputViewChanged"
            Parameter { name: "view"; type: "QAbstract3DInputHandler::InputView" }
        }
        Signal {
            name: "sceneChanged"
            Parameter { name: "scene"; type: "Q3DScene"; isPointer: true }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QAbstract3DSeries"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/Abstract3DSeries 1.0",
            "QtDataVisualization/Abstract3DSeries 1.1",
            "QtDataVisualization/Abstract3DSeries 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 257, 1536]
        Enum {
            name: "SeriesType"
            values: [
                "SeriesTypeNone",
                "SeriesTypeBar",
                "SeriesTypeScatter",
                "SeriesTypeSurface"
            ]
        }
        Enum {
            name: "Mesh"
            values: [
                "MeshUserDefined",
                "MeshBar",
                "MeshCube",
                "MeshPyramid",
                "MeshCone",
                "MeshCylinder",
                "MeshBevelBar",
                "MeshBevelCube",
                "MeshSphere",
                "MeshMinimal",
                "MeshArrow",
                "MeshPoint"
            ]
        }
        Property {
            name: "type"
            type: "SeriesType"
            read: "type"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
        Property {
            name: "itemLabelFormat"
            type: "QString"
            read: "itemLabelFormat"
            write: "setItemLabelFormat"
            notify: "itemLabelFormatChanged"
            index: 1
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibilityChanged"
            index: 2
        }
        Property {
            name: "mesh"
            type: "Mesh"
            read: "mesh"
            write: "setMesh"
            notify: "meshChanged"
            index: 3
        }
        Property {
            name: "meshSmooth"
            type: "bool"
            read: "isMeshSmooth"
            write: "setMeshSmooth"
            notify: "meshSmoothChanged"
            index: 4
        }
        Property {
            name: "meshRotation"
            type: "QQuaternion"
            read: "meshRotation"
            write: "setMeshRotation"
            notify: "meshRotationChanged"
            index: 5
        }
        Property {
            name: "userDefinedMesh"
            type: "QString"
            read: "userDefinedMesh"
            write: "setUserDefinedMesh"
            notify: "userDefinedMeshChanged"
            index: 6
        }
        Property {
            name: "colorStyle"
            type: "Q3DTheme::ColorStyle"
            read: "colorStyle"
            write: "setColorStyle"
            notify: "colorStyleChanged"
            index: 7
        }
        Property {
            name: "baseColor"
            type: "QColor"
            read: "baseColor"
            write: "setBaseColor"
            notify: "baseColorChanged"
            index: 8
        }
        Property {
            name: "baseGradient"
            type: "QLinearGradient"
            read: "baseGradient"
            write: "setBaseGradient"
            notify: "baseGradientChanged"
            index: 9
        }
        Property {
            name: "singleHighlightColor"
            type: "QColor"
            read: "singleHighlightColor"
            write: "setSingleHighlightColor"
            notify: "singleHighlightColorChanged"
            index: 10
        }
        Property {
            name: "singleHighlightGradient"
            type: "QLinearGradient"
            read: "singleHighlightGradient"
            write: "setSingleHighlightGradient"
            notify: "singleHighlightGradientChanged"
            index: 11
        }
        Property {
            name: "multiHighlightColor"
            type: "QColor"
            read: "multiHighlightColor"
            write: "setMultiHighlightColor"
            notify: "multiHighlightColorChanged"
            index: 12
        }
        Property {
            name: "multiHighlightGradient"
            type: "QLinearGradient"
            read: "multiHighlightGradient"
            write: "setMultiHighlightGradient"
            notify: "multiHighlightGradientChanged"
            index: 13
        }
        Property {
            name: "name"
            type: "QString"
            read: "name"
            write: "setName"
            notify: "nameChanged"
            index: 14
        }
        Property {
            name: "itemLabel"
            revision: 257
            type: "QString"
            read: "itemLabel"
            notify: "itemLabelChanged"
            index: 15
            isReadonly: true
        }
        Property {
            name: "itemLabelVisible"
            revision: 257
            type: "bool"
            read: "isItemLabelVisible"
            write: "setItemLabelVisible"
            notify: "itemLabelVisibilityChanged"
            index: 16
        }
        Signal {
            name: "itemLabelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "visibilityChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "meshChanged"
            Parameter { name: "mesh"; type: "QAbstract3DSeries::Mesh" }
        }
        Signal {
            name: "meshSmoothChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "meshRotationChanged"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Signal {
            name: "userDefinedMeshChanged"
            Parameter { name: "fileName"; type: "QString" }
        }
        Signal {
            name: "colorStyleChanged"
            Parameter { name: "style"; type: "Q3DTheme::ColorStyle" }
        }
        Signal {
            name: "baseColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "baseGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "singleHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "singleHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "multiHighlightColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "multiHighlightGradientChanged"
            Parameter { name: "gradient"; type: "QLinearGradient" }
        }
        Signal {
            name: "nameChanged"
            Parameter { name: "name"; type: "QString" }
        }
        Signal {
            name: "itemLabelChanged"
            revision: 257
            Parameter { name: "label"; type: "QString" }
        }
        Signal {
            name: "itemLabelVisibilityChanged"
            revision: 257
            Parameter { name: "visible"; type: "bool" }
        }
        Method {
            name: "setMeshAxisAndAngle"
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QAbstractDataProxy"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/AbstractDataProxy 1.0",
            "QtDataVisualization/AbstractDataProxy 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Enum {
            name: "DataType"
            values: [
                "DataTypeNone",
                "DataTypeBar",
                "DataTypeScatter",
                "DataTypeSurface"
            ]
        }
        Property {
            name: "type"
            type: "DataType"
            read: "type"
            index: 0
            isReadonly: true
            isPropertyConstant: true
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QAbstractItemModel"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/AbstractItemModel 1.0",
            "QtDataVisualization/AbstractItemModel 6.0",
            "QtDataVisualization/AbstractItemModel 6.4"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536, 1540]
        Enum {
            name: "LayoutChangeHint"
            values: [
                "NoLayoutChangeHint",
                "VerticalSortHint",
                "HorizontalSortHint"
            ]
        }
        Enum {
            name: "CheckIndexOption"
            isScoped: true
            values: [
                "NoOption",
                "IndexIsValid",
                "DoNotUseParent",
                "ParentIsInvalid"
            ]
        }
        Signal {
            name: "dataChanged"
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
            Parameter { name: "roles"; type: "int"; isList: true }
        }
        Signal {
            name: "dataChanged"
            isCloned: true
            Parameter { name: "topLeft"; type: "QModelIndex" }
            Parameter { name: "bottomRight"; type: "QModelIndex" }
        }
        Signal {
            name: "headerDataChanged"
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "layoutChanged"
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutChanged"
            isCloned: true
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
        }
        Signal { name: "layoutChanged"; isCloned: true }
        Signal {
            name: "layoutAboutToBeChanged"
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
            Parameter { name: "hint"; type: "QAbstractItemModel::LayoutChangeHint" }
        }
        Signal {
            name: "layoutAboutToBeChanged"
            isCloned: true
            Parameter { name: "parents"; type: "QPersistentModelIndex"; isList: true }
        }
        Signal { name: "layoutAboutToBeChanged"; isCloned: true }
        Signal {
            name: "rowsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsInserted"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal {
            name: "columnsRemoved"
            Parameter { name: "parent"; type: "QModelIndex" }
            Parameter { name: "first"; type: "int" }
            Parameter { name: "last"; type: "int" }
        }
        Signal { name: "modelAboutToBeReset" }
        Signal { name: "modelReset" }
        Signal {
            name: "rowsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "rowsMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationRow"; type: "int" }
        }
        Signal {
            name: "columnsAboutToBeMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Signal {
            name: "columnsMoved"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceStart"; type: "int" }
            Parameter { name: "sourceEnd"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationColumn"; type: "int" }
        }
        Method { name: "submit"; type: "bool" }
        Method { name: "revert" }
        Method { name: "resetInternalData" }
        Method {
            name: "hasIndex"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "hasIndex"
            type: "bool"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "index"
            type: "QModelIndex"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "parent"
            type: "QModelIndex"
            isMethodConstant: true
            Parameter { name: "child"; type: "QModelIndex" }
        }
        Method {
            name: "sibling"
            type: "QModelIndex"
            isMethodConstant: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "column"; type: "int" }
            Parameter { name: "idx"; type: "QModelIndex" }
        }
        Method {
            name: "rowCount"
            type: "int"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "rowCount"; type: "int"; isCloned: true; isMethodConstant: true }
        Method {
            name: "columnCount"
            type: "int"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "columnCount"; type: "int"; isCloned: true; isMethodConstant: true }
        Method {
            name: "hasChildren"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method { name: "hasChildren"; type: "bool"; isCloned: true; isMethodConstant: true }
        Method {
            name: "data"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "data"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "setData"
            type: "bool"
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "setData"
            type: "bool"
            isCloned: true
            Parameter { name: "index"; type: "QModelIndex" }
            Parameter { name: "value"; type: "QVariant" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            isMethodConstant: true
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
            Parameter { name: "role"; type: "int" }
        }
        Method {
            name: "headerData"
            type: "QVariant"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "section"; type: "int" }
            Parameter { name: "orientation"; type: "Qt::Orientation" }
        }
        Method {
            name: "insertRows"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertRows"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "insertColumns"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertColumns"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "removeRows"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeRows"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "removeColumns"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeColumns"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Method {
            name: "moveRows"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceRow"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "moveColumns"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceColumn"; type: "int" }
            Parameter { name: "count"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "insertRow"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertRow"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "insertColumn"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "insertColumn"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "removeRow"
            revision: 1540
            type: "bool"
            Parameter { name: "row"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeRow"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "row"; type: "int" }
        }
        Method {
            name: "removeColumn"
            revision: 1540
            type: "bool"
            Parameter { name: "column"; type: "int" }
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "removeColumn"
            revision: 1540
            type: "bool"
            isCloned: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "moveRow"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceRow"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "moveColumn"
            revision: 1540
            type: "bool"
            Parameter { name: "sourceParent"; type: "QModelIndex" }
            Parameter { name: "sourceColumn"; type: "int" }
            Parameter { name: "destinationParent"; type: "QModelIndex" }
            Parameter { name: "destinationChild"; type: "int" }
        }
        Method {
            name: "fetchMore"
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "canFetchMore"
            type: "bool"
            isMethodConstant: true
            Parameter { name: "parent"; type: "QModelIndex" }
        }
        Method {
            name: "flags"
            type: "Qt::ItemFlags"
            isMethodConstant: true
            Parameter { name: "index"; type: "QModelIndex" }
        }
        Method {
            name: "sort"
            revision: 1540
            Parameter { name: "column"; type: "int" }
            Parameter { name: "order"; type: "Qt::SortOrder" }
        }
        Method {
            name: "sort"
            revision: 1540
            isCloned: true
            Parameter { name: "column"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isMethodConstant: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
            Parameter { name: "flags"; type: "Qt::MatchFlags" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
            Parameter { name: "hits"; type: "int" }
        }
        Method {
            name: "match"
            type: "QModelIndexList"
            isCloned: true
            isMethodConstant: true
            Parameter { name: "start"; type: "QModelIndex" }
            Parameter { name: "role"; type: "int" }
            Parameter { name: "value"; type: "QVariant" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QBar3DSeries"
        accessSemantics: "reference"
        prototype: "QAbstract3DSeries"
        exports: [
            "QtDataVisualization/QBar3DSeries 1.0",
            "QtDataVisualization/QBar3DSeries 1.1",
            "QtDataVisualization/QBar3DSeries 6.0",
            "QtDataVisualization/QBar3DSeries 6.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 257, 1536, 1539]
        Property {
            name: "dataProxy"
            type: "QBarDataProxy"
            isPointer: true
            read: "dataProxy"
            write: "setDataProxy"
            notify: "dataProxyChanged"
            index: 0
        }
        Property {
            name: "selectedBar"
            type: "QPoint"
            read: "selectedBar"
            write: "setSelectedBar"
            notify: "selectedBarChanged"
            index: 1
        }
        Property {
            name: "meshAngle"
            type: "float"
            read: "meshAngle"
            write: "setMeshAngle"
            notify: "meshAngleChanged"
            index: 2
        }
        Property {
            name: "rowColors"
            revision: 1539
            type: "QColor"
            isList: true
            read: "rowColors"
            write: "setRowColors"
            notify: "rowColorsChanged"
            index: 3
        }
        Signal {
            name: "dataProxyChanged"
            Parameter { name: "proxy"; type: "QBarDataProxy"; isPointer: true }
        }
        Signal {
            name: "selectedBarChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal {
            name: "meshAngleChanged"
            Parameter { name: "angle"; type: "float" }
        }
        Signal {
            name: "rowColorsChanged"
            revision: 1539
            Parameter { name: "rowcolors"; type: "QColor"; isList: true }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QBarDataProxy"
        accessSemantics: "reference"
        prototype: "QAbstractDataProxy"
        exports: [
            "QtDataVisualization/BarDataProxy 1.0",
            "QtDataVisualization/BarDataProxy 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "rowCount"
            type: "int"
            read: "rowCount"
            notify: "rowCountChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "rowLabels"
            type: "QStringList"
            read: "rowLabels"
            write: "setRowLabels"
            notify: "rowLabelsChanged"
            index: 1
        }
        Property {
            name: "columnLabels"
            type: "QStringList"
            read: "columnLabels"
            write: "setColumnLabels"
            notify: "columnLabelsChanged"
            index: 2
        }
        Property {
            name: "series"
            type: "QBar3DSeries"
            isPointer: true
            read: "series"
            notify: "seriesChanged"
            index: 3
            isReadonly: true
        }
        Signal { name: "arrayReset" }
        Signal {
            name: "rowsAdded"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "rowsChanged"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "itemChanged"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "columnIndex"; type: "int" }
        }
        Signal {
            name: "rowCountChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal { name: "rowLabelsChanged" }
        Signal { name: "columnLabelsChanged" }
        Signal {
            name: "seriesChanged"
            Parameter { name: "series"; type: "QBar3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QCategory3DAxis"
        accessSemantics: "reference"
        prototype: "QAbstract3DAxis"
        exports: [
            "QtDataVisualization/CategoryAxis3D 1.0",
            "QtDataVisualization/CategoryAxis3D 1.1",
            "QtDataVisualization/CategoryAxis3D 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "labels"
            type: "QStringList"
            read: "labels"
            write: "setLabels"
            notify: "labelsChanged"
            index: 0
        }
        Signal { name: "labelsChanged" }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QCustom3DItem"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/Custom3DItem 1.1",
            "QtDataVisualization/Custom3DItem 1.2",
            "QtDataVisualization/Custom3DItem 6.0"
        ]
        exportMetaObjectRevisions: [257, 258, 1536]
        Property {
            name: "meshFile"
            type: "QString"
            read: "meshFile"
            write: "setMeshFile"
            notify: "meshFileChanged"
            index: 0
        }
        Property {
            name: "textureFile"
            type: "QString"
            read: "textureFile"
            write: "setTextureFile"
            notify: "textureFileChanged"
            index: 1
        }
        Property {
            name: "position"
            type: "QVector3D"
            read: "position"
            write: "setPosition"
            notify: "positionChanged"
            index: 2
        }
        Property {
            name: "positionAbsolute"
            type: "bool"
            read: "isPositionAbsolute"
            write: "setPositionAbsolute"
            notify: "positionAbsoluteChanged"
            index: 3
        }
        Property {
            name: "scaling"
            type: "QVector3D"
            read: "scaling"
            write: "setScaling"
            notify: "scalingChanged"
            index: 4
        }
        Property {
            name: "rotation"
            type: "QQuaternion"
            read: "rotation"
            write: "setRotation"
            notify: "rotationChanged"
            index: 5
        }
        Property {
            name: "visible"
            type: "bool"
            read: "isVisible"
            write: "setVisible"
            notify: "visibleChanged"
            index: 6
        }
        Property {
            name: "shadowCasting"
            type: "bool"
            read: "isShadowCasting"
            write: "setShadowCasting"
            notify: "shadowCastingChanged"
            index: 7
        }
        Property {
            name: "scalingAbsolute"
            revision: 258
            type: "bool"
            read: "isScalingAbsolute"
            write: "setScalingAbsolute"
            notify: "scalingAbsoluteChanged"
            index: 8
        }
        Signal {
            name: "meshFileChanged"
            Parameter { name: "meshFile"; type: "QString" }
        }
        Signal {
            name: "textureFileChanged"
            Parameter { name: "textureFile"; type: "QString" }
        }
        Signal {
            name: "positionChanged"
            Parameter { name: "position"; type: "QVector3D" }
        }
        Signal {
            name: "positionAbsoluteChanged"
            Parameter { name: "positionAbsolute"; type: "bool" }
        }
        Signal {
            name: "scalingChanged"
            Parameter { name: "scaling"; type: "QVector3D" }
        }
        Signal {
            name: "rotationChanged"
            Parameter { name: "rotation"; type: "QQuaternion" }
        }
        Signal {
            name: "visibleChanged"
            Parameter { name: "visible"; type: "bool" }
        }
        Signal {
            name: "shadowCastingChanged"
            Parameter { name: "shadowCasting"; type: "bool" }
        }
        Signal {
            name: "scalingAbsoluteChanged"
            revision: 258
            Parameter { name: "scalingAbsolute"; type: "bool" }
        }
        Method {
            name: "setRotationAxisAndAngle"
            Parameter { name: "axis"; type: "QVector3D" }
            Parameter { name: "angle"; type: "float" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QCustom3DLabel"
        accessSemantics: "reference"
        prototype: "QCustom3DItem"
        exports: [
            "QtDataVisualization/Custom3DLabel 1.1",
            "QtDataVisualization/Custom3DLabel 1.2",
            "QtDataVisualization/Custom3DLabel 6.0"
        ]
        exportMetaObjectRevisions: [257, 258, 1536]
        Property {
            name: "text"
            type: "QString"
            read: "text"
            write: "setText"
            notify: "textChanged"
            index: 0
        }
        Property {
            name: "font"
            type: "QFont"
            read: "font"
            write: "setFont"
            notify: "fontChanged"
            index: 1
        }
        Property {
            name: "textColor"
            type: "QColor"
            read: "textColor"
            write: "setTextColor"
            notify: "textColorChanged"
            index: 2
        }
        Property {
            name: "backgroundColor"
            type: "QColor"
            read: "backgroundColor"
            write: "setBackgroundColor"
            notify: "backgroundColorChanged"
            index: 3
        }
        Property {
            name: "borderEnabled"
            type: "bool"
            read: "isBorderEnabled"
            write: "setBorderEnabled"
            notify: "borderEnabledChanged"
            index: 4
        }
        Property {
            name: "backgroundEnabled"
            type: "bool"
            read: "isBackgroundEnabled"
            write: "setBackgroundEnabled"
            notify: "backgroundEnabledChanged"
            index: 5
        }
        Property {
            name: "facingCamera"
            type: "bool"
            read: "isFacingCamera"
            write: "setFacingCamera"
            notify: "facingCameraChanged"
            index: 6
        }
        Signal {
            name: "textChanged"
            Parameter { name: "text"; type: "QString" }
        }
        Signal {
            name: "fontChanged"
            Parameter { name: "font"; type: "QFont" }
        }
        Signal {
            name: "textColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "backgroundColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "borderEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "backgroundEnabledChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "facingCameraChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QCustom3DVolume"
        accessSemantics: "reference"
        prototype: "QCustom3DItem"
        exports: [
            "QtDataVisualization/Custom3DVolume 1.2",
            "QtDataVisualization/Custom3DVolume 6.0"
        ]
        exportMetaObjectRevisions: [258, 1536]
        Property {
            name: "textureWidth"
            type: "int"
            read: "textureWidth"
            write: "setTextureWidth"
            notify: "textureWidthChanged"
            index: 0
        }
        Property {
            name: "textureHeight"
            type: "int"
            read: "textureHeight"
            write: "setTextureHeight"
            notify: "textureHeightChanged"
            index: 1
        }
        Property {
            name: "textureDepth"
            type: "int"
            read: "textureDepth"
            write: "setTextureDepth"
            notify: "textureDepthChanged"
            index: 2
        }
        Property {
            name: "sliceIndexX"
            type: "int"
            read: "sliceIndexX"
            write: "setSliceIndexX"
            notify: "sliceIndexXChanged"
            index: 3
        }
        Property {
            name: "sliceIndexY"
            type: "int"
            read: "sliceIndexY"
            write: "setSliceIndexY"
            notify: "sliceIndexYChanged"
            index: 4
        }
        Property {
            name: "sliceIndexZ"
            type: "int"
            read: "sliceIndexZ"
            write: "setSliceIndexZ"
            notify: "sliceIndexZChanged"
            index: 5
        }
        Property {
            name: "colorTable"
            type: "QRgb"
            isList: true
            read: "colorTable"
            write: "setColorTable"
            notify: "colorTableChanged"
            index: 6
        }
        Property {
            name: "textureData"
            type: "QList<uchar>"
            isPointer: true
            read: "textureData"
            write: "setTextureData"
            notify: "textureDataChanged"
            index: 7
        }
        Property {
            name: "alphaMultiplier"
            type: "float"
            read: "alphaMultiplier"
            write: "setAlphaMultiplier"
            notify: "alphaMultiplierChanged"
            index: 8
        }
        Property {
            name: "preserveOpacity"
            type: "bool"
            read: "preserveOpacity"
            write: "setPreserveOpacity"
            notify: "preserveOpacityChanged"
            index: 9
        }
        Property {
            name: "useHighDefShader"
            type: "bool"
            read: "useHighDefShader"
            write: "setUseHighDefShader"
            notify: "useHighDefShaderChanged"
            index: 10
        }
        Property {
            name: "drawSlices"
            type: "bool"
            read: "drawSlices"
            write: "setDrawSlices"
            notify: "drawSlicesChanged"
            index: 11
        }
        Property {
            name: "drawSliceFrames"
            type: "bool"
            read: "drawSliceFrames"
            write: "setDrawSliceFrames"
            notify: "drawSliceFramesChanged"
            index: 12
        }
        Property {
            name: "sliceFrameColor"
            type: "QColor"
            read: "sliceFrameColor"
            write: "setSliceFrameColor"
            notify: "sliceFrameColorChanged"
            index: 13
        }
        Property {
            name: "sliceFrameWidths"
            type: "QVector3D"
            read: "sliceFrameWidths"
            write: "setSliceFrameWidths"
            notify: "sliceFrameWidthsChanged"
            index: 14
        }
        Property {
            name: "sliceFrameGaps"
            type: "QVector3D"
            read: "sliceFrameGaps"
            write: "setSliceFrameGaps"
            notify: "sliceFrameGapsChanged"
            index: 15
        }
        Property {
            name: "sliceFrameThicknesses"
            type: "QVector3D"
            read: "sliceFrameThicknesses"
            write: "setSliceFrameThicknesses"
            notify: "sliceFrameThicknessesChanged"
            index: 16
        }
        Signal {
            name: "textureWidthChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "textureHeightChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "textureDepthChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "sliceIndexXChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "sliceIndexYChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal {
            name: "sliceIndexZChanged"
            Parameter { name: "value"; type: "int" }
        }
        Signal { name: "colorTableChanged" }
        Signal {
            name: "textureDataChanged"
            Parameter { name: "data"; type: "QList<uchar>"; isPointer: true }
        }
        Signal {
            name: "textureFormatChanged"
            Parameter { name: "format"; type: "QImage::Format" }
        }
        Signal {
            name: "alphaMultiplierChanged"
            Parameter { name: "mult"; type: "float" }
        }
        Signal {
            name: "preserveOpacityChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "useHighDefShaderChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "drawSlicesChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "drawSliceFramesChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "sliceFrameColorChanged"
            Parameter { name: "color"; type: "QColor" }
        }
        Signal {
            name: "sliceFrameWidthsChanged"
            Parameter { name: "values"; type: "QVector3D" }
        }
        Signal {
            name: "sliceFrameGapsChanged"
            Parameter { name: "values"; type: "QVector3D" }
        }
        Signal {
            name: "sliceFrameThicknessesChanged"
            Parameter { name: "values"; type: "QVector3D" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QHeightMapSurfaceDataProxy"
        accessSemantics: "reference"
        prototype: "QSurfaceDataProxy"
        exports: [
            "QtDataVisualization/HeightMapSurfaceDataProxy 1.0",
            "QtDataVisualization/HeightMapSurfaceDataProxy 6.0",
            "QtDataVisualization/HeightMapSurfaceDataProxy 6.3"
        ]
        exportMetaObjectRevisions: [256, 1536, 1539]
        Property {
            name: "heightMap"
            type: "QImage"
            read: "heightMap"
            write: "setHeightMap"
            notify: "heightMapChanged"
            index: 0
        }
        Property {
            name: "heightMapFile"
            type: "QString"
            read: "heightMapFile"
            write: "setHeightMapFile"
            notify: "heightMapFileChanged"
            index: 1
        }
        Property {
            name: "minXValue"
            type: "float"
            read: "minXValue"
            write: "setMinXValue"
            notify: "minXValueChanged"
            index: 2
        }
        Property {
            name: "maxXValue"
            type: "float"
            read: "maxXValue"
            write: "setMaxXValue"
            notify: "maxXValueChanged"
            index: 3
        }
        Property {
            name: "minZValue"
            type: "float"
            read: "minZValue"
            write: "setMinZValue"
            notify: "minZValueChanged"
            index: 4
        }
        Property {
            name: "maxZValue"
            type: "float"
            read: "maxZValue"
            write: "setMaxZValue"
            notify: "maxZValueChanged"
            index: 5
        }
        Property {
            name: "minYValue"
            revision: 1539
            type: "float"
            read: "minYValue"
            write: "setMinYValue"
            notify: "minYValueChanged"
            index: 6
        }
        Property {
            name: "maxYValue"
            revision: 1539
            type: "float"
            read: "maxYValue"
            write: "setMaxYValue"
            notify: "maxYValueChanged"
            index: 7
        }
        Property {
            name: "autoScaleY"
            revision: 1539
            type: "bool"
            read: "autoScaleY"
            write: "setAutoScaleY"
            notify: "autoScaleYChanged"
            index: 8
        }
        Signal {
            name: "heightMapChanged"
            Parameter { name: "image"; type: "QImage" }
        }
        Signal {
            name: "heightMapFileChanged"
            Parameter { name: "filename"; type: "QString" }
        }
        Signal {
            name: "minXValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxXValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "minZValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxZValueChanged"
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "minYValueChanged"
            revision: 1539
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "maxYValueChanged"
            revision: 1539
            Parameter { name: "value"; type: "float" }
        }
        Signal {
            name: "autoScaleYChanged"
            revision: 1539
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QItemModelBarDataProxy"
        accessSemantics: "reference"
        prototype: "QBarDataProxy"
        exports: [
            "QtDataVisualization/ItemModelBarDataProxy 1.0",
            "QtDataVisualization/ItemModelBarDataProxy 1.1",
            "QtDataVisualization/ItemModelBarDataProxy 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Enum {
            name: "MultiMatchBehavior"
            values: ["MMBFirst", "MMBLast", "MMBAverage", "MMBCumulative"]
        }
        Property {
            name: "itemModel"
            type: "QAbstractItemModel"
            isPointer: true
            read: "itemModel"
            write: "setItemModel"
            notify: "itemModelChanged"
            index: 0
        }
        Property {
            name: "rowRole"
            type: "QString"
            read: "rowRole"
            write: "setRowRole"
            notify: "rowRoleChanged"
            index: 1
        }
        Property {
            name: "columnRole"
            type: "QString"
            read: "columnRole"
            write: "setColumnRole"
            notify: "columnRoleChanged"
            index: 2
        }
        Property {
            name: "valueRole"
            type: "QString"
            read: "valueRole"
            write: "setValueRole"
            notify: "valueRoleChanged"
            index: 3
        }
        Property {
            name: "rotationRole"
            type: "QString"
            read: "rotationRole"
            write: "setRotationRole"
            notify: "rotationRoleChanged"
            index: 4
        }
        Property {
            name: "rowCategories"
            type: "QStringList"
            read: "rowCategories"
            write: "setRowCategories"
            notify: "rowCategoriesChanged"
            index: 5
        }
        Property {
            name: "columnCategories"
            type: "QStringList"
            read: "columnCategories"
            write: "setColumnCategories"
            notify: "columnCategoriesChanged"
            index: 6
        }
        Property {
            name: "useModelCategories"
            type: "bool"
            read: "useModelCategories"
            write: "setUseModelCategories"
            notify: "useModelCategoriesChanged"
            index: 7
        }
        Property {
            name: "autoRowCategories"
            type: "bool"
            read: "autoRowCategories"
            write: "setAutoRowCategories"
            notify: "autoRowCategoriesChanged"
            index: 8
        }
        Property {
            name: "autoColumnCategories"
            type: "bool"
            read: "autoColumnCategories"
            write: "setAutoColumnCategories"
            notify: "autoColumnCategoriesChanged"
            index: 9
        }
        Property {
            name: "rowRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "rowRolePattern"
            write: "setRowRolePattern"
            notify: "rowRolePatternChanged"
            index: 10
        }
        Property {
            name: "columnRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "columnRolePattern"
            write: "setColumnRolePattern"
            notify: "columnRolePatternChanged"
            index: 11
        }
        Property {
            name: "valueRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "valueRolePattern"
            write: "setValueRolePattern"
            notify: "valueRolePatternChanged"
            index: 12
        }
        Property {
            name: "rotationRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "rotationRolePattern"
            write: "setRotationRolePattern"
            notify: "rotationRolePatternChanged"
            index: 13
        }
        Property {
            name: "rowRoleReplace"
            revision: 257
            type: "QString"
            read: "rowRoleReplace"
            write: "setRowRoleReplace"
            notify: "rowRoleReplaceChanged"
            index: 14
        }
        Property {
            name: "columnRoleReplace"
            revision: 257
            type: "QString"
            read: "columnRoleReplace"
            write: "setColumnRoleReplace"
            notify: "columnRoleReplaceChanged"
            index: 15
        }
        Property {
            name: "valueRoleReplace"
            revision: 257
            type: "QString"
            read: "valueRoleReplace"
            write: "setValueRoleReplace"
            notify: "valueRoleReplaceChanged"
            index: 16
        }
        Property {
            name: "rotationRoleReplace"
            revision: 257
            type: "QString"
            read: "rotationRoleReplace"
            write: "setRotationRoleReplace"
            notify: "rotationRoleReplaceChanged"
            index: 17
        }
        Property {
            name: "multiMatchBehavior"
            revision: 257
            type: "MultiMatchBehavior"
            read: "multiMatchBehavior"
            write: "setMultiMatchBehavior"
            notify: "multiMatchBehaviorChanged"
            index: 18
        }
        Signal {
            name: "itemModelChanged"
            Parameter { name: "itemModel"; type: "QAbstractItemModel"; isPointer: true; isTypeConstant: true }
        }
        Signal {
            name: "rowRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "columnRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "valueRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "rotationRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal { name: "rowCategoriesChanged" }
        Signal { name: "columnCategoriesChanged" }
        Signal {
            name: "useModelCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoRowCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoColumnCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "rowRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "columnRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "valueRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rotationRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rowRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "columnRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "valueRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "rotationRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "multiMatchBehaviorChanged"
            revision: 257
            Parameter { name: "behavior"; type: "MultiMatchBehavior" }
        }
        Method {
            name: "rowCategoryIndex"
            type: "int"
            Parameter { name: "category"; type: "QString" }
        }
        Method {
            name: "columnCategoryIndex"
            type: "int"
            Parameter { name: "category"; type: "QString" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QItemModelScatterDataProxy"
        accessSemantics: "reference"
        prototype: "QScatterDataProxy"
        exports: [
            "QtDataVisualization/ItemModelScatterDataProxy 1.0",
            "QtDataVisualization/ItemModelScatterDataProxy 1.1",
            "QtDataVisualization/ItemModelScatterDataProxy 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "itemModel"
            type: "QAbstractItemModel"
            isPointer: true
            read: "itemModel"
            write: "setItemModel"
            notify: "itemModelChanged"
            index: 0
        }
        Property {
            name: "xPosRole"
            type: "QString"
            read: "xPosRole"
            write: "setXPosRole"
            notify: "xPosRoleChanged"
            index: 1
        }
        Property {
            name: "yPosRole"
            type: "QString"
            read: "yPosRole"
            write: "setYPosRole"
            notify: "yPosRoleChanged"
            index: 2
        }
        Property {
            name: "zPosRole"
            type: "QString"
            read: "zPosRole"
            write: "setZPosRole"
            notify: "zPosRoleChanged"
            index: 3
        }
        Property {
            name: "rotationRole"
            type: "QString"
            read: "rotationRole"
            write: "setRotationRole"
            notify: "rotationRoleChanged"
            index: 4
        }
        Property {
            name: "xPosRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "xPosRolePattern"
            write: "setXPosRolePattern"
            notify: "xPosRolePatternChanged"
            index: 5
        }
        Property {
            name: "yPosRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "yPosRolePattern"
            write: "setYPosRolePattern"
            notify: "yPosRolePatternChanged"
            index: 6
        }
        Property {
            name: "zPosRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "zPosRolePattern"
            write: "setZPosRolePattern"
            notify: "zPosRolePatternChanged"
            index: 7
        }
        Property {
            name: "rotationRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "rotationRolePattern"
            write: "setRotationRolePattern"
            notify: "rotationRolePatternChanged"
            index: 8
        }
        Property {
            name: "xPosRoleReplace"
            revision: 257
            type: "QString"
            read: "xPosRoleReplace"
            write: "setXPosRoleReplace"
            notify: "xPosRoleReplaceChanged"
            index: 9
        }
        Property {
            name: "yPosRoleReplace"
            revision: 257
            type: "QString"
            read: "yPosRoleReplace"
            write: "setYPosRoleReplace"
            notify: "yPosRoleReplaceChanged"
            index: 10
        }
        Property {
            name: "zPosRoleReplace"
            revision: 257
            type: "QString"
            read: "zPosRoleReplace"
            write: "setZPosRoleReplace"
            notify: "zPosRoleReplaceChanged"
            index: 11
        }
        Property {
            name: "rotationRoleReplace"
            revision: 257
            type: "QString"
            read: "rotationRoleReplace"
            write: "setRotationRoleReplace"
            notify: "rotationRoleReplaceChanged"
            index: 12
        }
        Signal {
            name: "itemModelChanged"
            Parameter { name: "itemModel"; type: "QAbstractItemModel"; isPointer: true; isTypeConstant: true }
        }
        Signal {
            name: "xPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "yPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "zPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "rotationRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "xPosRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "yPosRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "zPosRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rotationRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rotationRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "xPosRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "yPosRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "zPosRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QItemModelSurfaceDataProxy"
        accessSemantics: "reference"
        prototype: "QSurfaceDataProxy"
        exports: [
            "QtDataVisualization/ItemModelSurfaceDataProxy 1.0",
            "QtDataVisualization/ItemModelSurfaceDataProxy 1.1",
            "QtDataVisualization/ItemModelSurfaceDataProxy 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Enum {
            name: "MultiMatchBehavior"
            values: ["MMBFirst", "MMBLast", "MMBAverage", "MMBCumulativeY"]
        }
        Property {
            name: "itemModel"
            type: "QAbstractItemModel"
            isPointer: true
            read: "itemModel"
            write: "setItemModel"
            notify: "itemModelChanged"
            index: 0
        }
        Property {
            name: "rowRole"
            type: "QString"
            read: "rowRole"
            write: "setRowRole"
            notify: "rowRoleChanged"
            index: 1
        }
        Property {
            name: "columnRole"
            type: "QString"
            read: "columnRole"
            write: "setColumnRole"
            notify: "columnRoleChanged"
            index: 2
        }
        Property {
            name: "xPosRole"
            type: "QString"
            read: "xPosRole"
            write: "setXPosRole"
            notify: "xPosRoleChanged"
            index: 3
        }
        Property {
            name: "yPosRole"
            type: "QString"
            read: "yPosRole"
            write: "setYPosRole"
            notify: "yPosRoleChanged"
            index: 4
        }
        Property {
            name: "zPosRole"
            type: "QString"
            read: "zPosRole"
            write: "setZPosRole"
            notify: "zPosRoleChanged"
            index: 5
        }
        Property {
            name: "rowCategories"
            type: "QStringList"
            read: "rowCategories"
            write: "setRowCategories"
            notify: "rowCategoriesChanged"
            index: 6
        }
        Property {
            name: "columnCategories"
            type: "QStringList"
            read: "columnCategories"
            write: "setColumnCategories"
            notify: "columnCategoriesChanged"
            index: 7
        }
        Property {
            name: "useModelCategories"
            type: "bool"
            read: "useModelCategories"
            write: "setUseModelCategories"
            notify: "useModelCategoriesChanged"
            index: 8
        }
        Property {
            name: "autoRowCategories"
            type: "bool"
            read: "autoRowCategories"
            write: "setAutoRowCategories"
            notify: "autoRowCategoriesChanged"
            index: 9
        }
        Property {
            name: "autoColumnCategories"
            type: "bool"
            read: "autoColumnCategories"
            write: "setAutoColumnCategories"
            notify: "autoColumnCategoriesChanged"
            index: 10
        }
        Property {
            name: "rowRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "rowRolePattern"
            write: "setRowRolePattern"
            notify: "rowRolePatternChanged"
            index: 11
        }
        Property {
            name: "columnRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "columnRolePattern"
            write: "setColumnRolePattern"
            notify: "columnRolePatternChanged"
            index: 12
        }
        Property {
            name: "xPosRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "xPosRolePattern"
            write: "setXPosRolePattern"
            notify: "xPosRolePatternChanged"
            index: 13
        }
        Property {
            name: "yPosRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "yPosRolePattern"
            write: "setYPosRolePattern"
            notify: "yPosRolePatternChanged"
            index: 14
        }
        Property {
            name: "zPosRolePattern"
            revision: 257
            type: "QRegularExpression"
            read: "zPosRolePattern"
            write: "setZPosRolePattern"
            notify: "zPosRolePatternChanged"
            index: 15
        }
        Property {
            name: "rowRoleReplace"
            revision: 257
            type: "QString"
            read: "rowRoleReplace"
            write: "setRowRoleReplace"
            notify: "rowRoleReplaceChanged"
            index: 16
        }
        Property {
            name: "columnRoleReplace"
            revision: 257
            type: "QString"
            read: "columnRoleReplace"
            write: "setColumnRoleReplace"
            notify: "columnRoleReplaceChanged"
            index: 17
        }
        Property {
            name: "xPosRoleReplace"
            revision: 257
            type: "QString"
            read: "xPosRoleReplace"
            write: "setXPosRoleReplace"
            notify: "xPosRoleReplaceChanged"
            index: 18
        }
        Property {
            name: "yPosRoleReplace"
            revision: 257
            type: "QString"
            read: "yPosRoleReplace"
            write: "setYPosRoleReplace"
            notify: "yPosRoleReplaceChanged"
            index: 19
        }
        Property {
            name: "zPosRoleReplace"
            revision: 257
            type: "QString"
            read: "zPosRoleReplace"
            write: "setZPosRoleReplace"
            notify: "zPosRoleReplaceChanged"
            index: 20
        }
        Property {
            name: "multiMatchBehavior"
            revision: 257
            type: "MultiMatchBehavior"
            read: "multiMatchBehavior"
            write: "setMultiMatchBehavior"
            notify: "multiMatchBehaviorChanged"
            index: 21
        }
        Signal {
            name: "itemModelChanged"
            Parameter { name: "itemModel"; type: "QAbstractItemModel"; isPointer: true; isTypeConstant: true }
        }
        Signal {
            name: "rowRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "columnRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "xPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "yPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal {
            name: "zPosRoleChanged"
            Parameter { name: "role"; type: "QString" }
        }
        Signal { name: "rowCategoriesChanged" }
        Signal { name: "columnCategoriesChanged" }
        Signal {
            name: "useModelCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoRowCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "autoColumnCategoriesChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "rowRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "columnRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "xPosRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "yPosRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "zPosRolePatternChanged"
            revision: 257
            Parameter { name: "pattern"; type: "QRegularExpression" }
        }
        Signal {
            name: "rowRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "columnRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "xPosRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "yPosRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "zPosRoleReplaceChanged"
            revision: 257
            Parameter { name: "replace"; type: "QString" }
        }
        Signal {
            name: "multiMatchBehaviorChanged"
            revision: 257
            Parameter { name: "behavior"; type: "MultiMatchBehavior" }
        }
        Method {
            name: "rowCategoryIndex"
            type: "int"
            Parameter { name: "category"; type: "QString" }
        }
        Method {
            name: "columnCategoryIndex"
            type: "int"
            Parameter { name: "category"; type: "QString" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QLogValue3DAxisFormatter"
        accessSemantics: "reference"
        prototype: "QValue3DAxisFormatter"
        exports: [
            "QtDataVisualization/LogValueAxis3DFormatter 1.1",
            "QtDataVisualization/LogValueAxis3DFormatter 6.0"
        ]
        exportMetaObjectRevisions: [257, 1536]
        Property {
            name: "base"
            type: "double"
            read: "base"
            write: "setBase"
            notify: "baseChanged"
            index: 0
        }
        Property {
            name: "autoSubGrid"
            type: "bool"
            read: "autoSubGrid"
            write: "setAutoSubGrid"
            notify: "autoSubGridChanged"
            index: 1
        }
        Property {
            name: "showEdgeLabels"
            type: "bool"
            read: "showEdgeLabels"
            write: "setShowEdgeLabels"
            notify: "showEdgeLabelsChanged"
            index: 2
        }
        Signal {
            name: "baseChanged"
            Parameter { name: "base"; type: "double" }
        }
        Signal {
            name: "autoSubGridChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
        Signal {
            name: "showEdgeLabelsChanged"
            Parameter { name: "enabled"; type: "bool" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QScatter3DSeries"
        accessSemantics: "reference"
        prototype: "QAbstract3DSeries"
        exports: [
            "QtDataVisualization/QScatter3DSeries 1.0",
            "QtDataVisualization/QScatter3DSeries 1.1",
            "QtDataVisualization/QScatter3DSeries 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "dataProxy"
            type: "QScatterDataProxy"
            isPointer: true
            read: "dataProxy"
            write: "setDataProxy"
            notify: "dataProxyChanged"
            index: 0
        }
        Property {
            name: "selectedItem"
            type: "int"
            read: "selectedItem"
            write: "setSelectedItem"
            notify: "selectedItemChanged"
            index: 1
        }
        Property {
            name: "itemSize"
            type: "float"
            read: "itemSize"
            write: "setItemSize"
            notify: "itemSizeChanged"
            index: 2
        }
        Signal {
            name: "dataProxyChanged"
            Parameter { name: "proxy"; type: "QScatterDataProxy"; isPointer: true }
        }
        Signal {
            name: "selectedItemChanged"
            Parameter { name: "index"; type: "int" }
        }
        Signal {
            name: "itemSizeChanged"
            Parameter { name: "size"; type: "float" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QScatterDataProxy"
        accessSemantics: "reference"
        prototype: "QAbstractDataProxy"
        exports: [
            "QtDataVisualization/ScatterDataProxy 1.0",
            "QtDataVisualization/ScatterDataProxy 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "itemCount"
            type: "int"
            read: "itemCount"
            notify: "itemCountChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "series"
            type: "QScatter3DSeries"
            isPointer: true
            read: "series"
            notify: "seriesChanged"
            index: 1
            isReadonly: true
        }
        Signal { name: "arrayReset" }
        Signal {
            name: "itemsAdded"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "itemsChanged"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "itemsRemoved"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "itemsInserted"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "itemCountChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "seriesChanged"
            Parameter { name: "series"; type: "QScatter3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QSurface3DSeries"
        accessSemantics: "reference"
        prototype: "QAbstract3DSeries"
        exports: [
            "QtDataVisualization/QSurface3DSeries 1.0",
            "QtDataVisualization/QSurface3DSeries 1.1",
            "QtDataVisualization/QSurface3DSeries 6.0",
            "QtDataVisualization/QSurface3DSeries 6.3"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 257, 1536, 1539]
        Enum {
            name: "DrawFlag"
            values: [
                "DrawWireframe",
                "DrawSurface",
                "DrawSurfaceAndWireframe"
            ]
        }
        Enum {
            name: "DrawFlags"
            alias: "DrawFlag"
            isFlag: true
            values: [
                "DrawWireframe",
                "DrawSurface",
                "DrawSurfaceAndWireframe"
            ]
        }
        Property {
            name: "dataProxy"
            type: "QSurfaceDataProxy"
            isPointer: true
            read: "dataProxy"
            write: "setDataProxy"
            notify: "dataProxyChanged"
            index: 0
        }
        Property {
            name: "selectedPoint"
            type: "QPoint"
            read: "selectedPoint"
            write: "setSelectedPoint"
            notify: "selectedPointChanged"
            index: 1
        }
        Property {
            name: "flatShadingEnabled"
            type: "bool"
            read: "isFlatShadingEnabled"
            write: "setFlatShadingEnabled"
            notify: "flatShadingEnabledChanged"
            index: 2
        }
        Property {
            name: "flatShadingSupported"
            type: "bool"
            read: "isFlatShadingSupported"
            notify: "flatShadingSupportedChanged"
            index: 3
            isReadonly: true
        }
        Property {
            name: "drawMode"
            type: "DrawFlags"
            read: "drawMode"
            write: "setDrawMode"
            notify: "drawModeChanged"
            index: 4
        }
        Property {
            name: "texture"
            type: "QImage"
            read: "texture"
            write: "setTexture"
            notify: "textureChanged"
            index: 5
        }
        Property {
            name: "textureFile"
            type: "QString"
            read: "textureFile"
            write: "setTextureFile"
            notify: "textureFileChanged"
            index: 6
        }
        Property {
            name: "wireframeColor"
            revision: 1539
            type: "QColor"
            read: "wireframeColor"
            write: "setWireframeColor"
            notify: "wireframeColorChanged"
            index: 7
        }
        Signal {
            name: "dataProxyChanged"
            Parameter { name: "proxy"; type: "QSurfaceDataProxy"; isPointer: true }
        }
        Signal {
            name: "selectedPointChanged"
            Parameter { name: "position"; type: "QPoint" }
        }
        Signal {
            name: "flatShadingEnabledChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "flatShadingSupportedChanged"
            Parameter { name: "enable"; type: "bool" }
        }
        Signal {
            name: "drawModeChanged"
            Parameter { name: "mode"; type: "QSurface3DSeries::DrawFlags" }
        }
        Signal {
            name: "textureChanged"
            Parameter { name: "image"; type: "QImage" }
        }
        Signal {
            name: "textureFileChanged"
            Parameter { name: "filename"; type: "QString" }
        }
        Signal {
            name: "wireframeColorChanged"
            revision: 1539
            Parameter { name: "color"; type: "QColor" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QSurfaceDataProxy"
        accessSemantics: "reference"
        prototype: "QAbstractDataProxy"
        exports: [
            "QtDataVisualization/SurfaceDataProxy 1.0",
            "QtDataVisualization/SurfaceDataProxy 6.0"
        ]
        isCreatable: false
        exportMetaObjectRevisions: [256, 1536]
        Property {
            name: "rowCount"
            type: "int"
            read: "rowCount"
            notify: "rowCountChanged"
            index: 0
            isReadonly: true
        }
        Property {
            name: "columnCount"
            type: "int"
            read: "columnCount"
            notify: "columnCountChanged"
            index: 1
            isReadonly: true
        }
        Property {
            name: "series"
            type: "QSurface3DSeries"
            isPointer: true
            read: "series"
            notify: "seriesChanged"
            index: 2
            isReadonly: true
        }
        Signal { name: "arrayReset" }
        Signal {
            name: "rowsAdded"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "rowsChanged"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "rowsRemoved"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "rowsInserted"
            Parameter { name: "startIndex"; type: "int" }
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "itemChanged"
            Parameter { name: "rowIndex"; type: "int" }
            Parameter { name: "columnIndex"; type: "int" }
        }
        Signal {
            name: "rowCountChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "columnCountChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "seriesChanged"
            Parameter { name: "series"; type: "QSurface3DSeries"; isPointer: true }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QTouch3DInputHandler"
        accessSemantics: "reference"
        prototype: "Q3DInputHandler"
        exports: [
            "QtDataVisualization/TouchInputHandler3D 1.2",
            "QtDataVisualization/TouchInputHandler3D 6.0"
        ]
        exportMetaObjectRevisions: [258, 1536]
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QValue3DAxis"
        accessSemantics: "reference"
        prototype: "QAbstract3DAxis"
        exports: [
            "QtDataVisualization/ValueAxis3D 1.0",
            "QtDataVisualization/ValueAxis3D 1.1",
            "QtDataVisualization/ValueAxis3D 6.0"
        ]
        exportMetaObjectRevisions: [256, 257, 1536]
        Property {
            name: "segmentCount"
            type: "int"
            read: "segmentCount"
            write: "setSegmentCount"
            notify: "segmentCountChanged"
            index: 0
        }
        Property {
            name: "subSegmentCount"
            type: "int"
            read: "subSegmentCount"
            write: "setSubSegmentCount"
            notify: "subSegmentCountChanged"
            index: 1
        }
        Property {
            name: "labelFormat"
            type: "QString"
            read: "labelFormat"
            write: "setLabelFormat"
            notify: "labelFormatChanged"
            index: 2
        }
        Property {
            name: "formatter"
            revision: 257
            type: "QValue3DAxisFormatter"
            isPointer: true
            read: "formatter"
            write: "setFormatter"
            notify: "formatterChanged"
            index: 3
        }
        Property {
            name: "reversed"
            revision: 257
            type: "bool"
            read: "reversed"
            write: "setReversed"
            notify: "reversedChanged"
            index: 4
        }
        Signal {
            name: "segmentCountChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "subSegmentCountChanged"
            Parameter { name: "count"; type: "int" }
        }
        Signal {
            name: "labelFormatChanged"
            Parameter { name: "format"; type: "QString" }
        }
        Signal {
            name: "formatterChanged"
            revision: 257
            Parameter { name: "formatter"; type: "QValue3DAxisFormatter"; isPointer: true }
        }
        Signal {
            name: "reversedChanged"
            revision: 257
            Parameter { name: "enable"; type: "bool" }
        }
    }
    Component {
        file: "private/foreigntypes_p.h"
        name: "QValue3DAxisFormatter"
        accessSemantics: "reference"
        prototype: "QObject"
        exports: [
            "QtDataVisualization/ValueAxis3DFormatter 1.1",
            "QtDataVisualization/ValueAxis3DFormatter 6.0"
        ]
        exportMetaObjectRevisions: [257, 1536]
    }
}
